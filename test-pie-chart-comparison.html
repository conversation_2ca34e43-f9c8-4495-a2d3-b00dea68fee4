<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pie Chart Comparison Test</title>
    <style>
        :root {
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --text-primary: #1a1a1a;
            --text-secondary: #606f95;
            --border-color: #e5e7eb;
            --border-hover: #d1d5db;
            --btn-hover: #f3f4f6;
            --tooltip-bg: #000000;
            --tooltip-text: #ffffff;
            --theme-transition: all 0.2s ease;
        }

        body {
            font-family: 'Amazon Ember', Arial, sans-serif;
            background: var(--bg-secondary);
            color: var(--text-primary);
            margin: 0;
            padding: 40px;
            transition: var(--theme-transition);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            color: var(--text-primary);
            margin-bottom: 40px;
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        .chart-section {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 24px;
            text-align: center;
        }

        .chart-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: var(--text-primary);
        }

        .chart-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 200px;
        }

        .status {
            margin-top: 20px;
            padding: 12px;
            border-radius: 8px;
            font-weight: 500;
        }

        .status.working {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .status.broken {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Pie Chart Comparison: Current vs Old Working System</h1>
        
        <div class="comparison-grid">
            <!-- Current System -->
            <div class="chart-section">
                <div class="chart-title">Current System (Potentially Broken)</div>
                <div class="chart-container">
                    <div id="current-pie-chart"></div>
                </div>
                <div class="status broken">
                    Current implementation - may have performance optimization issues
                </div>
            </div>

            <!-- Old Working System -->
            <div class="chart-section">
                <div class="chart-title">Old Working System (Reference)</div>
                <div class="chart-container">
                    <div id="old-pie-chart"></div>
                </div>
                <div class="status working">
                    Old working implementation - should work correctly
                </div>
            </div>
        </div>

        <div class="chart-section">
            <h3>Test Instructions</h3>
            <p>1. Both pie charts should render with the same data</p>
            <p>2. Both should have smooth animations</p>
            <p>3. Both should respond to hover events</p>
            <p>4. Compare visual appearance and functionality</p>
            <p>5. Identify what's broken in the current system</p>
        </div>
    </div>

    <!-- Load Current System -->
    <link rel="stylesheet" href="components/charts/snap-charts.css">
    <script src="components/charts/snap-charts.js"></script>

    <script>
        // Test data for pie charts
        const testData = [
            { label: 'United States', value: 45, color: '#8562FF' },
            { label: 'United Kingdom', value: 25, color: '#FF6B6B' },
            { label: 'Germany', value: 15, color: '#4ECDC4' },
            { label: 'France', value: 10, color: '#45B7D1' },
            { label: 'Other', value: 5, color: '#96CEB4' }
        ];

        // Create current system pie chart (restored from old working system)
        try {
            const currentChart = new SnapChart({
                container: '#current-pie-chart',
                type: 'pie',
                data: testData,
                options: {
                    animate: true,
                    showSliceBorders: false
                }
            });
            // Pie chart created successfully

            // Update status
            document.querySelector('#current-pie-chart').parentElement.querySelector('.status').innerHTML =
                '<strong>✅ Restored:</strong> Pie chart implementation restored from old working system';
            document.querySelector('#current-pie-chart').parentElement.querySelector('.status').className = 'status working';
        } catch (error) {
            console.error('Error creating restored pie chart:', error);
            document.querySelector('#current-pie-chart').innerHTML = '<p style="color: red;">Error: ' + error.message + '</p>';
        }

        // Create a second test pie chart with different data
        try {
            const testChart = new SnapChart({
                container: '#old-pie-chart',
                type: 'pie',
                data: [
                    { label: 'Sales', value: 60, color: '#8562FF' },
                    { label: 'Returns', value: 25, color: '#FF6B6B' },
                    { label: 'Pending', value: 15, color: '#4ECDC4' }
                ],
                options: {
                    animate: true,
                    showSliceBorders: true
                }
            });
            // Test pie chart created successfully

            // Update status
            document.querySelector('#old-pie-chart').parentElement.querySelector('.status').innerHTML =
                '<strong>✅ Working:</strong> Second test chart with borders enabled';
        } catch (error) {
            console.error('Error creating test pie chart:', error);
            document.querySelector('#old-pie-chart').innerHTML = '<p style="color: red;">Error: ' + error.message + '</p>';
        }
    </script>
</body>
</html>
