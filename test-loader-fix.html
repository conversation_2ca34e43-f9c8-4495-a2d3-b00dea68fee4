<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Loader Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        
        .test-container {
            background: #2a2a2a;
            border-radius: 14px;
            padding: 20px;
            margin: 20px 0;
            position: relative;
            min-height: 200px;
        }
        
        .test-results {
            background: #333;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>Loader Fix Test</h1>
    <p>This test validates that the SnapLoader.hideOverlay() function works correctly with container elements instead of ID strings.</p>
    
    <div class="test-container" id="test-container-1">
        <h3>Test Container 1</h3>
        <p>This container will test the corrected loader functionality.</p>
        <button onclick="testCorrectLoaderUsage()">Test Correct Loader Usage</button>
    </div>
    
    <div class="test-container" id="test-container-2">
        <h3>Test Container 2</h3>
        <p>This container will test the old (incorrect) loader usage for comparison.</p>
        <button onclick="testIncorrectLoaderUsage()">Test Incorrect Loader Usage</button>
    </div>
    
    <div class="test-results" id="test-results">
        Test results will appear here...
    </div>
    
    <script src="snapapp.js"></script>
    <script>
        function log(message, type = 'info') {
            const results = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
            results.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            
            // Auto-scroll to bottom
            results.scrollTop = results.scrollHeight;
        }
        
        function testCorrectLoaderUsage() {
            log('Testing CORRECT loader usage (passing container element)...');
            
            const container = document.getElementById('test-container-1');
            
            if (!window.SnapLoader) {
                log('SnapLoader not available', 'error');
                return;
            }
            
            try {
                // Show loader with container element
                log('Showing loader...');
                const overlay = window.SnapLoader.showOverlay(container, {
                    text: 'Testing correct usage...',
                    id: 'test-correct-loader'
                });
                
                if (overlay) {
                    log('Loader shown successfully', 'success');
                    
                    // Hide loader after 2 seconds using CONTAINER ELEMENT (correct way)
                    setTimeout(() => {
                        log('Hiding loader using container element...');
                        window.SnapLoader.hideOverlay(container);
                        log('Loader hidden successfully', 'success');
                    }, 2000);
                } else {
                    log('Failed to show loader', 'error');
                }
                
            } catch (error) {
                log(`Error in correct usage test: ${error.message}`, 'error');
            }
        }
        
        function testIncorrectLoaderUsage() {
            log('Testing INCORRECT loader usage (passing ID string)...');
            
            const container = document.getElementById('test-container-2');
            
            if (!window.SnapLoader) {
                log('SnapLoader not available', 'error');
                return;
            }
            
            try {
                // Show loader with container element
                log('Showing loader...');
                const overlay = window.SnapLoader.showOverlay(container, {
                    text: 'Testing incorrect usage...',
                    id: 'test-incorrect-loader'
                });
                
                if (overlay) {
                    log('Loader shown successfully', 'success');
                    
                    // Try to hide loader after 2 seconds using ID STRING (incorrect way)
                    setTimeout(() => {
                        log('Attempting to hide loader using ID string (this should fail)...');
                        window.SnapLoader.hideOverlay('test-incorrect-loader');
                        
                        // Check if loader is still visible after 1 second
                        setTimeout(() => {
                            const stillVisible = container.querySelector('.snap-loader-overlay');
                            if (stillVisible) {
                                log('Loader is still visible - hideOverlay with ID failed as expected', 'warning');
                                log('Manually hiding loader with container element...');
                                window.SnapLoader.hideOverlay(container);
                                log('Loader manually hidden', 'success');
                            } else {
                                log('Loader was hidden (unexpected)', 'warning');
                            }
                        }, 1000);
                        
                    }, 2000);
                } else {
                    log('Failed to show loader', 'error');
                }
                
            } catch (error) {
                log(`Error in incorrect usage test: ${error.message}`, 'error');
            }
        }
        
        // Initialize test
        document.addEventListener('DOMContentLoaded', () => {
            log('Loader fix test initialized');
            log('Click the buttons above to test loader functionality');
        });
    </script>
</body>
</html>
