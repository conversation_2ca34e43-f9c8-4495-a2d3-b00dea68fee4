<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monthly Sales Dropdown Auto-Close Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .test-description {
            margin-bottom: 15px;
            color: #666;
        }
        .test-button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .test-button:hover {
            background: #005a87;
        }
        .console-output {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 10px;
            margin-top: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .info {
            color: #17a2b8;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Monthly Sales Dropdown Auto-Close Behavior Test</h1>
        
        <div class="test-section">
            <div class="test-title">Test 1: Load Dashboard Component</div>
            <div class="test-description">
                This test loads the dashboard component and initializes the Monthly Sales dropdowns.
            </div>
            <button class="test-button" onclick="loadDashboard()">Load Dashboard</button>
            <div id="test1-output" class="console-output"></div>
        </div>

        <div class="test-section">
            <div class="test-title">Test 2: Verify Dropdown Manager Registration</div>
            <div class="test-description">
                This test checks if the MonthlySalesDropdownManager is properly initialized and dropdowns are registered.
            </div>
            <button class="test-button" onclick="testDropdownRegistration()">Test Registration</button>
            <div id="test2-output" class="console-output"></div>
        </div>

        <div class="test-section">
            <div class="test-title">Test 3: Test Auto-Close Behavior</div>
            <div class="test-description">
                This test simulates opening one dropdown and then opening another to verify auto-close behavior.
            </div>
            <button class="test-button" onclick="testAutoClose()">Test Auto-Close</button>
            <div id="test3-output" class="console-output"></div>
        </div>

        <div class="test-section">
            <div class="test-title">Test 4: Test Click Outside Behavior</div>
            <div class="test-description">
                This test simulates clicking outside dropdowns to verify they close properly.
            </div>
            <button class="test-button" onclick="testClickOutside()">Test Click Outside</button>
            <div id="test4-output" class="console-output"></div>
        </div>
    </div>

    <script>
        // Mock console logging for tests
        function logToTest(testId, message, type = 'info') {
            const output = document.getElementById(testId + '-output');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.textContent = `[${timestamp}] ${message}`;
            output.appendChild(logEntry);
            output.scrollTop = output.scrollHeight;
        }

        // Test 1: Load Dashboard Component
        function loadDashboard() {
            logToTest('test1', 'Starting dashboard load test...', 'info');
            
            try {
                // Create a mock dashboard container
                const dashboardContainer = document.createElement('div');
                dashboardContainer.innerHTML = `
                    <div class="monthly-sales-card">
                        <div class="controls-section">
                            <div class="compare-div">
                                <div class="compare-btn">Compare</div>
                                <div class="compare-dropdown" id="monthly-compare-dropdown">
                                    <div class="compare-dropdown-item" data-value="none">Don't compare</div>
                                    <div class="compare-dropdown-item" data-value="previous-year">Compare with previous year</div>
                                </div>
                            </div>
                            <div class="monthly-sales-year-dropdown snap-dropdown" id="monthlySalesYearDropdown">
                                <div class="dropdown-header" tabindex="0">
                                    <div class="dropdown-header-content">
                                        <span class="dropdown-selected-label">2025</span>
                                    </div>
                                </div>
                                <div class="dropdown-menu hidden">
                                    <div class="dropdown-list">
                                        <div class="dropdown-item" data-value="2025">2025</div>
                                        <div class="dropdown-item" data-value="2024">2024</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                document.body.appendChild(dashboardContainer);
                
                logToTest('test1', 'Dashboard HTML structure created', 'success');
                logToTest('test1', 'Ready for dropdown initialization', 'info');
                
            } catch (error) {
                logToTest('test1', `Error: ${error.message}`, 'error');
            }
        }

        // Test 2: Verify Dropdown Manager Registration
        function testDropdownRegistration() {
            logToTest('test2', 'Testing dropdown manager registration...', 'info');
            
            try {
                // Check if MonthlySalesDropdownManager exists
                if (typeof window.MonthlySalesDropdownManager !== 'undefined') {
                    logToTest('test2', 'MonthlySalesDropdownManager found globally', 'success');
                    
                    // Check if dropdowns are registered
                    const manager = window.MonthlySalesDropdownManager;
                    if (manager.dropdowns && manager.dropdowns.size > 0) {
                        logToTest('test2', `Found ${manager.dropdowns.size} registered dropdowns`, 'success');
                        manager.dropdowns.forEach((fns, id) => {
                            logToTest('test2', `- Dropdown registered: ${id}`, 'info');
                        });
                    } else {
                        logToTest('test2', 'No dropdowns registered yet', 'info');
                    }
                } else {
                    logToTest('test2', 'MonthlySalesDropdownManager not found', 'error');
                }
                
            } catch (error) {
                logToTest('test2', `Error: ${error.message}`, 'error');
            }
        }

        // Test 3: Test Auto-Close Behavior
        function testAutoClose() {
            logToTest('test3', 'Testing auto-close behavior...', 'info');
            
            try {
                if (typeof window.MonthlySalesDropdownManager !== 'undefined') {
                    const manager = window.MonthlySalesDropdownManager;
                    
                    // Simulate showing year dropdown
                    logToTest('test3', 'Simulating year dropdown open...', 'info');
                    if (manager.dropdowns.has('yearDropdown')) {
                        manager.show('yearDropdown');
                        logToTest('test3', `Active dropdown: ${manager.activeDropdown}`, 'info');
                        
                        // Simulate showing compare dropdown (should close year dropdown)
                        setTimeout(() => {
                            logToTest('test3', 'Simulating compare dropdown open...', 'info');
                            if (manager.dropdowns.has('compareDropdown')) {
                                manager.show('compareDropdown');
                                logToTest('test3', `Active dropdown after switch: ${manager.activeDropdown}`, 'success');
                                
                                if (manager.activeDropdown === 'compareDropdown') {
                                    logToTest('test3', 'Auto-close behavior working correctly!', 'success');
                                } else {
                                    logToTest('test3', 'Auto-close behavior not working', 'error');
                                }
                            } else {
                                logToTest('test3', 'Compare dropdown not registered', 'error');
                            }
                        }, 1000);
                    } else {
                        logToTest('test3', 'Year dropdown not registered', 'error');
                    }
                } else {
                    logToTest('test3', 'MonthlySalesDropdownManager not available', 'error');
                }
                
            } catch (error) {
                logToTest('test3', `Error: ${error.message}`, 'error');
            }
        }

        // Test 4: Test Click Outside Behavior
        function testClickOutside() {
            logToTest('test4', 'Testing click outside behavior...', 'info');
            
            try {
                if (typeof window.MonthlySalesDropdownManager !== 'undefined') {
                    const manager = window.MonthlySalesDropdownManager;
                    
                    // Show a dropdown first
                    if (manager.dropdowns.has('yearDropdown')) {
                        manager.show('yearDropdown');
                        logToTest('test4', 'Year dropdown opened', 'info');
                        
                        // Simulate click outside
                        setTimeout(() => {
                            logToTest('test4', 'Simulating click outside...', 'info');
                            manager.hideAll();
                            
                            if (manager.activeDropdown === null) {
                                logToTest('test4', 'Click outside behavior working correctly!', 'success');
                            } else {
                                logToTest('test4', 'Click outside behavior not working', 'error');
                            }
                        }, 1000);
                    } else {
                        logToTest('test4', 'Year dropdown not registered', 'error');
                    }
                } else {
                    logToTest('test4', 'MonthlySalesDropdownManager not available', 'error');
                }
                
            } catch (error) {
                logToTest('test4', `Error: ${error.message}`, 'error');
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded');
        });
    </script>
</body>
</html>
