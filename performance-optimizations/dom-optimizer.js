/**
 * DOM Operation Optimizer
 * Batches DOM operations and uses virtual scrolling for large lists
 */

class DOMOptimizer {
  constructor() {
    this.pendingOperations = [];
    this.isFlushScheduled = false;
  }

  /**
   * Batch DOM style changes to prevent layout thrashing
   */
  batchStyleChanges(operations) {
    return new Promise((resolve) => {
      // Add operations to pending queue
      this.pendingOperations.push(...operations);
      
      if (!this.isFlushScheduled) {
        this.isFlushScheduled = true;
        
        // Use requestAnimationFrame for optimal timing
        requestAnimationFrame(() => {
          this.flushOperations();
          this.isFlushScheduled = false;
          resolve();
        });
      }
    });
  }

  /**
   * Flush all pending DOM operations
   */
  flushOperations() {
    console.log(`🔄 Flushing ${this.pendingOperations.length} DOM operations`);
    
    // Group operations by type for better performance
    const styleOperations = [];
    const classOperations = [];
    const contentOperations = [];
    
    for (const op of this.pendingOperations) {
      switch (op.type) {
        case 'style':
          styleOperations.push(op);
          break;
        case 'class':
          classOperations.push(op);
          break;
        case 'content':
          contentOperations.push(op);
          break;
      }
    }
    
    // Execute operations in batches
    this.executeStyleOperations(styleOperations);
    this.executeClassOperations(classOperations);
    this.executeContentOperations(contentOperations);
    
    // Clear pending operations
    this.pendingOperations = [];
  }

  /**
   * Execute style operations efficiently
   */
  executeStyleOperations(operations) {
    for (const op of operations) {
      if (op.element && op.styles) {
        Object.assign(op.element.style, op.styles);
      }
    }
  }

  /**
   * Execute class operations efficiently
   */
  executeClassOperations(operations) {
    for (const op of operations) {
      if (op.element) {
        if (op.add) {
          op.element.classList.add(...op.add);
        }
        if (op.remove) {
          op.element.classList.remove(...op.remove);
        }
        if (op.toggle) {
          op.element.classList.toggle(op.toggle);
        }
      }
    }
  }

  /**
   * Execute content operations efficiently
   */
  executeContentOperations(operations) {
    for (const op of operations) {
      if (op.element) {
        if (op.textContent !== undefined) {
          op.element.textContent = op.textContent;
        }
        if (op.innerHTML !== undefined) {
          op.element.innerHTML = op.innerHTML;
        }
      }
    }
  }

  /**
   * Optimized marketplace filtering with batched DOM operations
   */
  async optimizeMarketplaceFiltering(listings, selectedMarketplace) {
    console.log(`🎯 Optimizing marketplace filtering for ${listings.length} listings`);
    
    const operations = [];
    
    // Prepare all operations without executing them
    listings.forEach((listing, index) => {
      const marketplaceFlag = listing.querySelector('.listing-marketplace-flag');
      let shouldShow = selectedMarketplace === 'all';
      
      if (!shouldShow && marketplaceFlag) {
        const flagSrc = marketplaceFlag.src;
        shouldShow = this.checkIfListingMatchesMarketplace(flagSrc, selectedMarketplace);
      }
      
      // Add style operation to queue
      operations.push({
        type: 'style',
        element: listing,
        styles: {
          display: shouldShow ? '' : 'none'
        }
      });
      
      // Handle divider visibility
      const divider = listing.nextElementSibling;
      if (divider && divider.classList.contains('listing-section-divider')) {
        operations.push({
          type: 'style',
          element: divider,
          styles: {
            display: shouldShow ? '' : 'none'
          }
        });
      }
    });
    
    // Execute all operations in a single batch
    await this.batchStyleChanges(operations);
    
    console.log(`✅ Marketplace filtering optimized`);
  }

  /**
   * Virtual scrolling for large lists
   */
  createVirtualScrollList(container, items, itemHeight, renderItem) {
    const containerHeight = container.clientHeight;
    const visibleCount = Math.ceil(containerHeight / itemHeight) + 2; // Buffer
    
    let scrollTop = 0;
    let startIndex = 0;
    
    // Create virtual container
    const virtualContainer = document.createElement('div');
    virtualContainer.style.height = `${items.length * itemHeight}px`;
    virtualContainer.style.position = 'relative';
    
    // Create visible items container
    const visibleContainer = document.createElement('div');
    visibleContainer.style.position = 'absolute';
    visibleContainer.style.top = '0';
    visibleContainer.style.width = '100%';
    
    virtualContainer.appendChild(visibleContainer);
    container.appendChild(virtualContainer);
    
    // Render visible items
    function renderVisibleItems() {
      const endIndex = Math.min(startIndex + visibleCount, items.length);
      
      // Clear existing items
      visibleContainer.innerHTML = '';
      
      // Render visible items
      for (let i = startIndex; i < endIndex; i++) {
        const itemElement = renderItem(items[i], i);
        itemElement.style.position = 'absolute';
        itemElement.style.top = `${i * itemHeight}px`;
        itemElement.style.height = `${itemHeight}px`;
        visibleContainer.appendChild(itemElement);
      }
    }
    
    // Handle scroll events
    const debouncedScroll = createDebouncedFunction(() => {
      scrollTop = container.scrollTop;
      const newStartIndex = Math.floor(scrollTop / itemHeight);
      
      if (newStartIndex !== startIndex) {
        startIndex = newStartIndex;
        renderVisibleItems();
      }
    }, 16); // ~60fps
    
    container.addEventListener('scroll', debouncedScroll, { passive: true });
    
    // Initial render
    renderVisibleItems();
    
    return {
      update: (newItems) => {
        items = newItems;
        virtualContainer.style.height = `${items.length * itemHeight}px`;
        renderVisibleItems();
      },
      destroy: () => {
        container.removeEventListener('scroll', debouncedScroll);
        container.removeChild(virtualContainer);
      }
    };
  }

  /**
   * Helper method for marketplace matching
   */
  checkIfListingMatchesMarketplace(flagSrc, marketplace) {
    // Implementation from original code
    const marketplaceMap = {
      'us': 'us-flag.svg',
      'uk': 'uk-flag.svg',
      'de': 'de-flag.svg',
      'fr': 'fr-flag.svg',
      'it': 'it-flag.svg',
      'es': 'es-flag.svg',
      'jp': 'jp-flag.svg'
    };
    
    const expectedFlag = marketplaceMap[marketplace.toLowerCase()];
    return expectedFlag && flagSrc.includes(expectedFlag);
  }
}

// Global DOM optimizer
window.DOMOptimizer = new DOMOptimizer();

// Export for global use
window.optimizeMarketplaceFiltering = (listings, selectedMarketplace) => {
  return window.DOMOptimizer.optimizeMarketplaceFiltering(listings, selectedMarketplace);
};
