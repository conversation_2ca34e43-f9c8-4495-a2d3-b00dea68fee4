/**
 * Memory Monitor for Chrome Extension
 * Tracks memory usage and triggers cleanup when needed
 */

class MemoryMonitor {
  constructor() {
    this.isMonitoring = false;
    this.monitorInterval = null;
    this.checkFrequency = 60000; // Check every minute
    this.memoryHistory = [];
    this.maxHistorySize = 100;
    
    // Memory thresholds for massive scale (in MB)
    this.thresholds = {
      warning: 500,    // 500MB - start warning (5M products needs more memory)
      critical: 1000,  // 1GB - force cleanup
      emergency: 1500  // 1.5GB - emergency cleanup
    };
    
    // Performance metrics
    this.metrics = {
      heapUsed: 0,
      heapTotal: 0,
      domNodes: 0,
      eventListeners: 0,
      cacheSize: 0,
      indexedDBSize: 0
    };
  }

  /**
   * Start memory monitoring
   */
  startMonitoring() {
    if (this.isMonitoring) return;
    
    console.log('🔍 Starting memory monitoring...');
    this.isMonitoring = true;
    
    // Initial check
    this.checkMemoryUsage();
    
    // Setup interval using tracked interval
    this.monitorInterval = window.EventCleanupManager.setInterval(
      () => this.checkMemoryUsage(),
      this.checkFrequency
    );
    
    console.log(`✅ Memory monitoring started (${this.checkFrequency}ms interval)`);
  }

  /**
   * Stop memory monitoring
   */
  stopMonitoring() {
    if (!this.isMonitoring) return;
    
    console.log('🛑 Stopping memory monitoring...');
    this.isMonitoring = false;
    
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.monitorInterval = null;
    }
    
    console.log('✅ Memory monitoring stopped');
  }

  /**
   * Check current memory usage
   */
  async checkMemoryUsage() {
    try {
      // Get browser memory info
      const memoryInfo = this.getBrowserMemoryInfo();
      
      // Get application metrics
      const appMetrics = await this.getApplicationMetrics();
      
      // Combine metrics
      this.metrics = { ...memoryInfo, ...appMetrics };
      
      // Add to history
      this.addToHistory(this.metrics);
      
      // Check thresholds and take action
      this.checkThresholds();
      
      // Log current status
      this.logMemoryStatus();
      
    } catch (error) {
      console.error('❌ Error checking memory usage:', error);
    }
  }

  /**
   * Get browser memory information
   */
  getBrowserMemoryInfo() {
    const info = {
      heapUsed: 0,
      heapTotal: 0,
      timestamp: Date.now()
    };
    
    // Use performance.memory if available (Chrome)
    if (performance.memory) {
      info.heapUsed = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
      info.heapTotal = Math.round(performance.memory.totalJSHeapSize / 1024 / 1024);
    }
    
    return info;
  }

  /**
   * Get application-specific metrics
   */
  async getApplicationMetrics() {
    const metrics = {};
    
    try {
      // DOM node count
      metrics.domNodes = document.querySelectorAll('*').length;
      
      // Event listener count
      if (window.EventCleanupManager) {
        const stats = window.EventCleanupManager.getStats();
        metrics.eventListeners = stats.listeners + stats.observers + stats.intervals + stats.timeouts;
      }
      
      // Cache size
      if (window.DataCacheManager) {
        const cacheStats = window.DataCacheManager.getStats();
        metrics.cacheSize = cacheStats.size;
      }
      
      // IndexedDB size estimate
      if (window.IndexedDBManager) {
        const dbStats = await window.IndexedDBManager.getStorageStats();
        metrics.indexedDBSize = Object.values(dbStats).reduce((total, store) => {
          return total + (store.recordCount || 0);
        }, 0);
      }
      
    } catch (error) {
      console.error('❌ Error getting application metrics:', error);
    }
    
    return metrics;
  }

  /**
   * Add metrics to history
   */
  addToHistory(metrics) {
    this.memoryHistory.push(metrics);
    
    // Limit history size
    if (this.memoryHistory.length > this.maxHistorySize) {
      this.memoryHistory.shift();
    }
  }

  /**
   * Check memory thresholds and trigger actions
   */
  checkThresholds() {
    const heapUsed = this.metrics.heapUsed;
    
    if (heapUsed >= this.thresholds.emergency) {
      console.error(`🚨 EMERGENCY: Memory usage ${heapUsed}MB exceeds emergency threshold!`);
      this.triggerEmergencyCleanup();
      
    } else if (heapUsed >= this.thresholds.critical) {
      console.warn(`⚠️ CRITICAL: Memory usage ${heapUsed}MB exceeds critical threshold!`);
      this.triggerCriticalCleanup();
      
    } else if (heapUsed >= this.thresholds.warning) {
      console.warn(`⚠️ WARNING: Memory usage ${heapUsed}MB exceeds warning threshold`);
      this.triggerWarningCleanup();
    }
  }

  /**
   * Trigger warning-level cleanup
   */
  triggerWarningCleanup() {
    console.log('🧹 Triggering warning-level cleanup...');
    
    // Clear old cache entries
    if (window.DataCacheManager) {
      window.DataCacheManager.clearCache();
    }
    
    // Clean up expired IndexedDB data
    if (window.IndexedDBManager) {
      Object.keys(window.IndexedDBManager.stores).forEach(storeName => {
        window.IndexedDBManager.cleanupExpiredData(storeName);
      });
    }
  }

  /**
   * Trigger critical-level cleanup
   */
  triggerCriticalCleanup() {
    console.log('🧹 Triggering critical-level cleanup...');
    
    // Do warning cleanup first
    this.triggerWarningCleanup();
    
    // Stop real-time updates temporarily
    if (window.RealTimeDataManager && window.RealTimeDataManager.isActive) {
      console.log('⏸️ Temporarily stopping real-time updates...');
      window.RealTimeDataManager.stopRealTimeUpdates();
      
      // Restart after 30 seconds
      setTimeout(() => {
        if (this.metrics.heapUsed < this.thresholds.critical) {
          console.log('▶️ Restarting real-time updates...');
          window.RealTimeDataManager.startRealTimeUpdates();
        }
      }, 30000);
    }
    
    // Force garbage collection if available
    if (window.gc) {
      window.gc();
    }
  }

  /**
   * Trigger emergency-level cleanup
   */
  triggerEmergencyCleanup() {
    console.log('🚨 Triggering emergency cleanup...');
    
    // Do critical cleanup first
    this.triggerCriticalCleanup();
    
    // Clear all non-essential data
    if (window.DataCacheManager) {
      window.DataCacheManager.cache.clear();
    }
    
    // Stop all real-time operations
    if (window.RealTimeDataManager) {
      window.RealTimeDataManager.stopRealTimeUpdates();
    }
    
    // Dispatch emergency event for UI to handle
    window.dispatchEvent(new CustomEvent('memoryEmergency', {
      detail: { heapUsed: this.metrics.heapUsed }
    }));
  }

  /**
   * Log current memory status
   */
  logMemoryStatus() {
    const status = this.getMemoryStatus();
    
    if (status.level === 'normal') {
      // Only log every 10 checks for normal status
      if (this.memoryHistory.length % 10 === 0) {
        console.log('💚 Memory status: Normal', status);
      }
    } else {
      console.log(`📊 Memory status: ${status.level}`, status);
    }
  }

  /**
   * Get current memory status
   */
  getMemoryStatus() {
    const heapUsed = this.metrics.heapUsed;
    let level = 'normal';
    
    if (heapUsed >= this.thresholds.emergency) {
      level = 'emergency';
    } else if (heapUsed >= this.thresholds.critical) {
      level = 'critical';
    } else if (heapUsed >= this.thresholds.warning) {
      level = 'warning';
    }
    
    return {
      level,
      heapUsed: `${heapUsed}MB`,
      heapTotal: `${this.metrics.heapTotal}MB`,
      domNodes: this.metrics.domNodes,
      eventListeners: this.metrics.eventListeners,
      cacheSize: this.metrics.cacheSize,
      indexedDBRecords: this.metrics.indexedDBSize,
      thresholds: this.thresholds
    };
  }

  /**
   * Get memory trend analysis
   */
  getMemoryTrend() {
    if (this.memoryHistory.length < 2) return 'insufficient_data';
    
    const recent = this.memoryHistory.slice(-5);
    const trend = recent[recent.length - 1].heapUsed - recent[0].heapUsed;
    
    if (trend > 10) return 'increasing';
    if (trend < -10) return 'decreasing';
    return 'stable';
  }

  /**
   * Export memory report
   */
  getMemoryReport() {
    return {
      current: this.getMemoryStatus(),
      trend: this.getMemoryTrend(),
      history: this.memoryHistory.slice(-10), // Last 10 entries
      thresholds: this.thresholds,
      isMonitoring: this.isMonitoring
    };
  }
}

// Global instance
window.MemoryMonitor = new MemoryMonitor();

// Auto-start monitoring
window.MemoryMonitor.startMonitoring();

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
  window.MemoryMonitor.stopMonitoring();
});

// Export for global use
window.getMemoryReport = () => window.MemoryMonitor.getMemoryReport();
window.forceMemoryCleanup = () => window.MemoryMonitor.triggerCriticalCleanup();
