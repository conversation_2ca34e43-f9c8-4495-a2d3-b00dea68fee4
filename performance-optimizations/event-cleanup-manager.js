/**
 * Event Listener Cleanup Manager
 * Prevents memory leaks by managing event listener lifecycle
 */

class EventCleanupManager {
  constructor() {
    this.listeners = new Map(); // Track all listeners
    this.observers = new Set(); // Track MutationObservers
    this.intervals = new Set(); // Track intervals
    this.timeouts = new Set(); // Track timeouts
  }

  /**
   * Add event listener with automatic cleanup tracking
   */
  addEventListener(element, event, handler, options = {}) {
    // Create unique key for this listener
    const key = `${element.tagName || 'window'}_${event}_${Date.now()}_${Math.random()}`;
    
    // Add the listener
    element.addEventListener(event, handler, options);
    
    // Track for cleanup
    this.listeners.set(key, {
      element,
      event,
      handler,
      options
    });
    
    console.log(`📝 Tracked event listener: ${event} on ${element.tagName || 'window'}`);
    
    return key; // Return key for manual removal if needed
  }

  /**
   * Remove specific event listener
   */
  removeEventListener(key) {
    const listener = this.listeners.get(key);
    if (listener) {
      listener.element.removeEventListener(listener.event, listener.handler, listener.options);
      this.listeners.delete(key);
      console.log(`🗑️ Removed event listener: ${listener.event}`);
    }
  }

  /**
   * Add MutationObserver with cleanup tracking
   */
  addMutationObserver(target, callback, options) {
    const observer = new MutationObserver(callback);
    observer.observe(target, options);
    this.observers.add(observer);
    
    console.log(`👁️ Tracked MutationObserver on:`, target);
    
    return observer;
  }

  /**
   * Add interval with cleanup tracking
   */
  setInterval(callback, delay) {
    const intervalId = setInterval(callback, delay);
    this.intervals.add(intervalId);
    
    console.log(`⏰ Tracked interval: ${delay}ms`);
    
    return intervalId;
  }

  /**
   * Add timeout with cleanup tracking
   */
  setTimeout(callback, delay) {
    const timeoutId = setTimeout(() => {
      callback();
      this.timeouts.delete(timeoutId); // Auto-remove completed timeouts
    }, delay);
    this.timeouts.add(timeoutId);
    
    return timeoutId;
  }

  /**
   * Clean up all tracked resources
   */
  cleanup() {
    console.log(`🧹 Starting cleanup of ${this.listeners.size} listeners, ${this.observers.size} observers, ${this.intervals.size} intervals, ${this.timeouts.size} timeouts`);
    
    // Remove all event listeners
    for (const [key, listener] of this.listeners) {
      try {
        listener.element.removeEventListener(listener.event, listener.handler, listener.options);
      } catch (error) {
        console.warn(`Failed to remove listener ${key}:`, error);
      }
    }
    this.listeners.clear();
    
    // Disconnect all observers
    for (const observer of this.observers) {
      try {
        observer.disconnect();
      } catch (error) {
        console.warn(`Failed to disconnect observer:`, error);
      }
    }
    this.observers.clear();
    
    // Clear all intervals
    for (const intervalId of this.intervals) {
      try {
        clearInterval(intervalId);
      } catch (error) {
        console.warn(`Failed to clear interval:`, error);
      }
    }
    this.intervals.clear();
    
    // Clear all timeouts
    for (const timeoutId of this.timeouts) {
      try {
        clearTimeout(timeoutId);
      } catch (error) {
        console.warn(`Failed to clear timeout:`, error);
      }
    }
    this.timeouts.clear();
    
    console.log(`✅ Cleanup completed`);
  }

  /**
   * Get cleanup statistics
   */
  getStats() {
    return {
      listeners: this.listeners.size,
      observers: this.observers.size,
      intervals: this.intervals.size,
      timeouts: this.timeouts.size
    };
  }
}

// Global cleanup manager
window.EventCleanupManager = new EventCleanupManager();

// Auto-cleanup on page unload
window.addEventListener('beforeunload', () => {
  window.EventCleanupManager.cleanup();
});

// Auto-cleanup on component unload
window.addEventListener('componentUnloaded', (e) => {
  if (e.detail.component === 'dashboard') {
    window.EventCleanupManager.cleanup();
  }
});

/**
 * Debounced function helper to prevent excessive calls
 */
function createDebouncedFunction(func, delay) {
  let timeoutId;
  return function(...args) {
    clearTimeout(timeoutId);
    timeoutId = window.EventCleanupManager.setTimeout(() => {
      func.apply(this, args);
    }, delay);
  };
}

// Export for global use
window.createDebouncedFunction = createDebouncedFunction;
