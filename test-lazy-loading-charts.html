<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Lazy Loading Charts - Phase 2 Implementation</title>
    <style>
        body {
            font-family: 'Amazon Ember', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ffc107;
        }
        
        .status-indicator.success { background: #28a745; }
        .status-indicator.error { background: #dc3545; }
        
        .test-description {
            color: #666;
            margin-bottom: 16px;
            line-height: 1.5;
        }
        
        .test-results {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .chart-test-container {
            height: 400px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin: 16px 0;
            position: relative;
        }
        
        .scroll-spacer {
            height: 800px;
            background: linear-gradient(135deg, #e9ecef 0%, #f8f9fa 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-size: 14px;
            margin: 20px 0;
            border-radius: 8px;
        }
        
        .performance-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }
        
        .metric-card {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 8px;
            text-align: center;
        }
        
        .metric-value {
            font-size: 24px;
            font-weight: 600;
            color: #470CED;
        }
        
        .metric-label {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
        }
        
        .test-button {
            background: #470CED;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 8px;
        }
        
        .test-button:hover {
            background: #3a0bc4;
        }
        
        .test-button.secondary {
            background: #6c757d;
        }
        
        .test-button.secondary:hover {
            background: #5a6268;
        }
    </style>
    
    <!-- Load all required dependencies -->
    <link rel="stylesheet" href="./performance-optimizations/lazy-loading-styles.css">
    <link rel="stylesheet" href="./components/dashboard/dashboard.css">
    <link rel="stylesheet" href="./components/snap-chart/snap-chart.css">
    
    <!-- Performance optimization scripts -->
    <script src="./performance-optimizations/memory-monitor.js"></script>
    <script src="./performance-optimizations/event-cleanup-manager.js"></script>
    <script src="./performance-optimizations/viewport-lazy-loader.js"></script>
    <script src="./performance-optimizations/dom-optimizer.js"></script>
    
    <!-- Core dependencies -->
    <script src="./components/snap-loader/snap-loader.js"></script>
    <script src="./components/snap-timezone/snap-timezone.js"></script>
    <script src="./components/snap-chart/snap-chart.js"></script>
</head>
<body>
    <div class="test-container">
        <h1>🚀 Phase 2: Lazy Loading Charts Test</h1>
        <p>Testing the implementation of lazy loading for dashboard charts to ensure performance improvements without breaking functionality.</p>
        
        <div class="test-section">
            <div class="test-title">
                <div class="status-indicator" id="infrastructure-status"></div>
                Infrastructure Check
            </div>
            <div class="test-description">
                Verifying that all required performance optimization components are loaded and available.
            </div>
            <div class="test-results" id="infrastructure-results"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">
                <div class="status-indicator" id="skeleton-status"></div>
                Skeleton Placeholder Test
            </div>
            <div class="test-description">
                Testing that chart containers show skeleton placeholders before lazy loading.
            </div>
            <div class="chart-test-container" id="skeleton-test-container">
                <!-- This will contain skeleton placeholder -->
                <div class="chart-skeleton">
                    <div class="chart-skeleton-header"></div>
                    <div class="chart-skeleton-bars">
                        <div class="chart-skeleton-bar"></div>
                        <div class="chart-skeleton-bar"></div>
                        <div class="chart-skeleton-bar"></div>
                        <div class="chart-skeleton-bar"></div>
                        <div class="chart-skeleton-bar"></div>
                        <div class="chart-skeleton-bar"></div>
                        <div class="chart-skeleton-bar"></div>
                    </div>
                </div>
            </div>
            <div class="test-results" id="skeleton-results"></div>
        </div>
        
        <div class="scroll-spacer">
            📏 Scroll down to test viewport-based lazy loading
        </div>
        
        <div class="test-section">
            <div class="test-title">
                <div class="status-indicator" id="lazy-loading-status"></div>
                Lazy Loading Test
            </div>
            <div class="test-description">
                Testing that charts load only when entering the viewport. This chart should load when you scroll to it.
            </div>
            <div class="chart-test-container" id="lazy-chart-container">
                <!-- Chart skeleton placeholder -->
                <div class="chart-skeleton">
                    <div class="chart-skeleton-header"></div>
                    <div class="chart-skeleton-bars">
                        <div class="chart-skeleton-bar"></div>
                        <div class="chart-skeleton-bar"></div>
                        <div class="chart-skeleton-bar"></div>
                        <div class="chart-skeleton-bar"></div>
                        <div class="chart-skeleton-bar"></div>
                        <div class="chart-skeleton-bar"></div>
                        <div class="chart-skeleton-bar"></div>
                    </div>
                </div>
            </div>
            <div class="test-results" id="lazy-loading-results"></div>
            <button class="test-button" onclick="testLazyLoading()">Test Lazy Loading</button>
            <button class="test-button secondary" onclick="scrollToChart()">Scroll to Chart</button>
        </div>
        
        <div class="test-section">
            <div class="test-title">
                <div class="status-indicator" id="performance-status"></div>
                Performance Metrics
            </div>
            <div class="test-description">
                Measuring the performance impact of lazy loading implementation.
            </div>
            <div class="performance-metrics">
                <div class="metric-card">
                    <div class="metric-value" id="initial-load-time">--</div>
                    <div class="metric-label">Initial Load Time (ms)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="memory-usage">--</div>
                    <div class="metric-label">Memory Usage (MB)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="charts-loaded">0</div>
                    <div class="metric-label">Charts Loaded</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="lazy-loader-stats">--</div>
                    <div class="metric-label">Lazy Loader Status</div>
                </div>
            </div>
            <div class="test-results" id="performance-results"></div>
            <button class="test-button" onclick="measurePerformance()">Measure Performance</button>
            <button class="test-button secondary" onclick="getMemoryStats()">Check Memory</button>
        </div>
    </div>
    
    <script>
        // Test execution starts here
        let testResults = {
            infrastructure: false,
            skeleton: false,
            lazyLoading: false,
            performance: false
        };
        
        let startTime = performance.now();
        
        // Test 1: Infrastructure Check
        function testInfrastructure() {
            const results = [];
            let allPassed = true;
            
            // Check ViewportLazyLoader
            if (window.ViewportLazyLoader) {
                results.push('✅ ViewportLazyLoader loaded');
            } else {
                results.push('❌ ViewportLazyLoader missing');
                allPassed = false;
            }
            
            // Check MemoryMonitor
            if (window.MemoryMonitor) {
                results.push('✅ MemoryMonitor loaded');
            } else {
                results.push('❌ MemoryMonitor missing');
                allPassed = false;
            }
            
            // Check EventCleanupManager
            if (window.EventCleanupManager) {
                results.push('✅ EventCleanupManager loaded');
            } else {
                results.push('❌ EventCleanupManager missing');
                allPassed = false;
            }
            
            // Check SnapChart
            if (window.SnapChart) {
                results.push('✅ SnapChart loaded');
            } else {
                results.push('❌ SnapChart missing');
                allPassed = false;
            }
            
            document.getElementById('infrastructure-results').textContent = results.join('\n');
            document.getElementById('infrastructure-status').className = 
                `status-indicator ${allPassed ? 'success' : 'error'}`;
            
            testResults.infrastructure = allPassed;
            return allPassed;
        }
        
        // Test 2: Skeleton Placeholder Test
        function testSkeletonPlaceholder() {
            const container = document.getElementById('skeleton-test-container');
            const skeleton = container.querySelector('.chart-skeleton');
            
            const results = [];
            let passed = true;
            
            if (skeleton) {
                results.push('✅ Skeleton placeholder found');
                
                // Check skeleton structure
                const header = skeleton.querySelector('.chart-skeleton-header');
                const bars = skeleton.querySelectorAll('.chart-skeleton-bar');
                
                if (header) {
                    results.push('✅ Skeleton header present');
                } else {
                    results.push('❌ Skeleton header missing');
                    passed = false;
                }
                
                if (bars.length >= 7) {
                    results.push(`✅ Skeleton bars present (${bars.length})`);
                } else {
                    results.push(`❌ Insufficient skeleton bars (${bars.length})`);
                    passed = false;
                }
                
                // Check CSS animations
                const computedStyle = window.getComputedStyle(skeleton);
                if (computedStyle.animation || computedStyle.animationName) {
                    results.push('✅ Skeleton animations active');
                } else {
                    results.push('⚠️ Skeleton animations not detected');
                }
                
            } else {
                results.push('❌ Skeleton placeholder not found');
                passed = false;
            }
            
            document.getElementById('skeleton-results').textContent = results.join('\n');
            document.getElementById('skeleton-status').className = 
                `status-indicator ${passed ? 'success' : 'error'}`;
            
            testResults.skeleton = passed;
            return passed;
        }
        
        // Test 3: Lazy Loading Test
        function testLazyLoading() {
            const container = document.getElementById('lazy-chart-container');
            const results = [];
            let passed = true;
            
            try {
                // Register the test container for lazy loading
                const componentId = window.ViewportLazyLoader.registerComponent(container, {
                    id: 'test-lazy-chart',
                    type: 'chart',
                    loadingText: 'Loading test chart...',
                    dataGenerator: async () => {
                        results.push('✅ Data generator called');
                        return { test: 'data' };
                    },
                    initFunction: async (element, data) => {
                        results.push('✅ Init function called');
                        
                        // Remove skeleton
                        const skeleton = element.querySelector('.chart-skeleton');
                        if (skeleton) {
                            skeleton.remove();
                            results.push('✅ Skeleton removed');
                        }
                        
                        // Add test chart content
                        element.innerHTML = '<div style="padding: 20px; text-align: center; background: #e8f5e8; border-radius: 8px;">✅ Test Chart Loaded Successfully!</div>';
                        results.push('✅ Chart content rendered');
                    }
                });
                
                results.push(`✅ Component registered with ID: ${componentId}`);
                
                // Get lazy loader stats
                const stats = window.ViewportLazyLoader.getStats();
                results.push(`📊 Lazy Loader Stats: ${JSON.stringify(stats)}`);
                
            } catch (error) {
                results.push(`❌ Error: ${error.message}`);
                passed = false;
            }
            
            document.getElementById('lazy-loading-results').textContent = results.join('\n');
            document.getElementById('lazy-loading-status').className = 
                `status-indicator ${passed ? 'success' : 'error'}`;
            
            testResults.lazyLoading = passed;
            return passed;
        }
        
        // Scroll to chart for testing
        function scrollToChart() {
            document.getElementById('lazy-chart-container').scrollIntoView({ 
                behavior: 'smooth',
                block: 'center'
            });
        }
        
        // Test 4: Performance Measurement
        function measurePerformance() {
            const loadTime = performance.now() - startTime;
            document.getElementById('initial-load-time').textContent = Math.round(loadTime);
            
            // Update charts loaded counter
            const stats = window.ViewportLazyLoader ? window.ViewportLazyLoader.getStats() : { loaded: 0 };
            document.getElementById('charts-loaded').textContent = stats.loaded || 0;
            document.getElementById('lazy-loader-stats').textContent = 
                `${stats.loaded || 0}/${stats.registered || 0}`;
            
            const results = [];
            results.push(`Initial load time: ${Math.round(loadTime)}ms`);
            results.push(`Charts loaded: ${stats.loaded || 0}`);
            results.push(`Lazy loader registered: ${stats.registered || 0}`);
            results.push(`Lazy loader pending: ${stats.pending || 0}`);
            
            document.getElementById('performance-results').textContent = results.join('\n');
            document.getElementById('performance-status').className = 'status-indicator success';
            
            testResults.performance = true;
        }
        
        // Memory stats
        function getMemoryStats() {
            if (window.MemoryMonitor) {
                const stats = window.MemoryMonitor.getStats();
                const memoryMB = Math.round(stats.usedJSHeapSize / 1024 / 1024);
                document.getElementById('memory-usage').textContent = memoryMB;
            } else {
                document.getElementById('memory-usage').textContent = 'N/A';
            }
        }
        
        // Run tests on load
        window.addEventListener('load', () => {
            setTimeout(() => {
                testInfrastructure();
                testSkeletonPlaceholder();
                measurePerformance();
                getMemoryStats();
                
                console.log('🧪 Lazy Loading Tests Complete:', testResults);
            }, 100);
        });
        
        // Update memory stats periodically
        setInterval(getMemoryStats, 2000);
    </script>
</body>
</html>
