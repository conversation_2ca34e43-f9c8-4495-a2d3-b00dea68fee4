<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Memory Leak Fixes Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      padding: 20px;
      background: #f5f5f5;
    }
    .test-container {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      margin-bottom: 20px;
    }
    .test-result {
      padding: 10px;
      border-radius: 4px;
      margin: 10px 0;
    }
    .success {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    .error {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    .info {
      background: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }
    button {
      background: #007bff;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      margin: 5px;
    }
    button:hover {
      background: #0056b3;
    }
    .stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 10px;
      margin: 20px 0;
    }
    .stat-card {
      background: #f8f9fa;
      padding: 15px;
      border-radius: 4px;
      text-align: center;
    }
    .stat-value {
      font-size: 24px;
      font-weight: bold;
      color: #007bff;
    }
    .stat-label {
      font-size: 14px;
      color: #6c757d;
    }
  </style>
</head>
<body>
  <h1>🧪 Memory Leak Fixes Test</h1>
  
  <div class="test-container">
    <h2>Performance Optimization Status</h2>
    <div id="optimization-status"></div>
  </div>

  <div class="test-container">
    <h2>Event Cleanup Manager Test</h2>
    <button onclick="testEventCleanup()">Test Event Cleanup</button>
    <button onclick="getCleanupStats()">Get Cleanup Stats</button>
    <div id="cleanup-results"></div>
  </div>

  <div class="test-container">
    <h2>Memory Monitor Test</h2>
    <button onclick="testMemoryMonitor()">Check Memory Status</button>
    <button onclick="forceMemoryCleanup()">Force Memory Cleanup</button>
    <div id="memory-results"></div>
  </div>

  <div class="test-container">
    <h2>Performance Statistics</h2>
    <div class="stats" id="performance-stats"></div>
  </div>

  <!-- Load Performance Optimization Scripts -->
  <script src="performance-optimizations/event-cleanup-manager.js"></script>
  <script src="performance-optimizations/data-cache-manager.js"></script>
  <script src="performance-optimizations/dom-optimizer.js"></script>
  <script src="performance-optimizations/viewport-lazy-loader.js"></script>
  <script src="performance-optimizations/indexeddb-manager.js"></script>
  <script src="performance-optimizations/realtime-data-manager.js"></script>
  <script src="performance-optimizations/memory-monitor.js"></script>

  <script>
    // Test functions
    function checkOptimizationStatus() {
      const status = document.getElementById('optimization-status');
      const checks = [
        { name: 'EventCleanupManager', available: !!window.EventCleanupManager },
        { name: 'DataCacheManager', available: !!window.DataCacheManager },
        { name: 'DOMOptimizer', available: !!window.DOMOptimizer },
        { name: 'ViewportLazyLoader', available: !!window.ViewportLazyLoader },
        { name: 'IndexedDBManager', available: !!window.IndexedDBManager },
        { name: 'MemoryMonitor', available: !!window.MemoryMonitor },
        { name: 'RealTimeDataManager', available: !!window.RealTimeDataManager }
      ];

      let html = '';
      checks.forEach(check => {
        const className = check.available ? 'success' : 'error';
        const icon = check.available ? '✅' : '❌';
        html += `<div class="test-result ${className}">${icon} ${check.name}: ${check.available ? 'Available' : 'Not Available'}</div>`;
      });

      status.innerHTML = html;
    }

    function testEventCleanup() {
      const results = document.getElementById('cleanup-results');
      
      if (!window.EventCleanupManager) {
        results.innerHTML = '<div class="test-result error">❌ EventCleanupManager not available</div>';
        return;
      }

      // Test adding and tracking event listeners
      const testElement = document.createElement('div');
      document.body.appendChild(testElement);

      // Add some test event listeners
      window.EventCleanupManager.addEventListener(testElement, 'click', () => {});
      window.EventCleanupManager.addEventListener(testElement, 'mouseenter', () => {});
      window.EventCleanupManager.addEventListener(testElement, 'mouseleave', () => {});

      // Get stats
      const stats = window.EventCleanupManager.getStats();
      
      results.innerHTML = `
        <div class="test-result success">✅ Event listeners added and tracked successfully</div>
        <div class="test-result info">📊 Current Stats: ${stats.listeners} listeners, ${stats.observers} observers, ${stats.intervals} intervals, ${stats.timeouts} timeouts</div>
      `;

      // Clean up test element
      document.body.removeChild(testElement);
    }

    function getCleanupStats() {
      const results = document.getElementById('cleanup-results');
      
      if (!window.EventCleanupManager) {
        results.innerHTML = '<div class="test-result error">❌ EventCleanupManager not available</div>';
        return;
      }

      const stats = window.EventCleanupManager.getStats();
      
      results.innerHTML = `
        <div class="test-result info">
          📊 EventCleanupManager Stats:<br>
          • Event Listeners: ${stats.listeners}<br>
          • Mutation Observers: ${stats.observers}<br>
          • Intervals: ${stats.intervals}<br>
          • Timeouts: ${stats.timeouts}
        </div>
      `;
    }

    function testMemoryMonitor() {
      const results = document.getElementById('memory-results');
      
      if (!window.MemoryMonitor) {
        results.innerHTML = '<div class="test-result error">❌ MemoryMonitor not available</div>';
        return;
      }

      const report = window.getMemoryReport();
      
      results.innerHTML = `
        <div class="test-result success">✅ Memory Monitor is active</div>
        <div class="test-result info">
          📊 Memory Status: ${report.current.level}<br>
          • Heap Used: ${report.current.heapUsed}<br>
          • Heap Total: ${report.current.heapTotal}<br>
          • DOM Nodes: ${report.current.domNodes}<br>
          • Event Listeners: ${report.current.eventListeners}<br>
          • Trend: ${report.trend}
        </div>
      `;
    }

    function forceMemoryCleanup() {
      const results = document.getElementById('memory-results');
      
      if (!window.forceMemoryCleanup) {
        results.innerHTML = '<div class="test-result error">❌ Memory cleanup function not available</div>';
        return;
      }

      window.forceMemoryCleanup();
      
      results.innerHTML = `
        <div class="test-result success">✅ Memory cleanup triggered</div>
        <div class="test-result info">🧹 Check console for cleanup details</div>
      `;
    }

    function updatePerformanceStats() {
      const statsContainer = document.getElementById('performance-stats');
      
      // Get browser memory info
      const memoryInfo = performance.memory ? {
        used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
        total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024)
      } : { used: 'N/A', total: 'N/A' };

      // Get DOM node count
      const domNodes = document.querySelectorAll('*').length;

      // Get cleanup stats if available
      const cleanupStats = window.EventCleanupManager ? window.EventCleanupManager.getStats() : null;

      statsContainer.innerHTML = `
        <div class="stat-card">
          <div class="stat-value">${memoryInfo.used}MB</div>
          <div class="stat-label">Memory Used</div>
        </div>
        <div class="stat-card">
          <div class="stat-value">${memoryInfo.total}MB</div>
          <div class="stat-label">Memory Total</div>
        </div>
        <div class="stat-card">
          <div class="stat-value">${domNodes}</div>
          <div class="stat-label">DOM Nodes</div>
        </div>
        <div class="stat-card">
          <div class="stat-value">${cleanupStats ? cleanupStats.listeners : 'N/A'}</div>
          <div class="stat-label">Tracked Listeners</div>
        </div>
      `;
    }

    // Initialize tests
    document.addEventListener('DOMContentLoaded', () => {
      checkOptimizationStatus();
      updatePerformanceStats();
      
      // Update stats every 5 seconds
      setInterval(updatePerformanceStats, 5000);
    });
  </script>
</body>
</html>
