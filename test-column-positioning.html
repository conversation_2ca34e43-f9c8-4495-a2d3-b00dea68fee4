<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SnapChart Column Positioning Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .chart-container {
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 10px 0;
        }
        h2 {
            color: #333;
            margin-bottom: 10px;
        }
        .description {
            color: #666;
            margin-bottom: 15px;
        }
        .expected-layout {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #4caf50;
        }
    </style>
</head>
<body>
    <h1>SnapChart Column Positioning Test</h1>
    
    <div class="test-container">
        <h2>Test 1: Stacked Column Chart with Comparison Mode</h2>
        <div class="description">
            This test verifies that comparison columns now appear on the left side of main columns.
            <div class="expected-layout">
                <strong>Expected Layout:</strong> [Comparison Column] [Gap] [Main Column]
            </div>
        </div>
        <div id="chart1" class="chart-container"></div>
    </div>

    <div class="test-container">
        <h2>Test 2: Monthly Sales with Last Week Comparison</h2>
        <div class="description">
            This test shows monthly sales data with last week's comparison data.
            <div class="expected-layout">
                <strong>Expected Layout:</strong> [Last Week Column] [Gap] [Current Month Column]
            </div>
        </div>
        <div id="chart2" class="chart-container"></div>
    </div>

    <div class="test-container">
        <h2>Test 3: Stacked Column without Comparison</h2>
        <div class="description">
            This test verifies that charts without comparison mode still work correctly.
        </div>
        <div id="chart3" class="chart-container"></div>
    </div>

    <script src="components/charts/snap-charts.js"></script>
    <script>
        // Test 1: Stacked Column Chart with Comparison Mode
        const chart1 = new SnapChart({
            container: document.getElementById('chart1'),
            type: 'stacked-column',
            data: [
                {
                    sales: 1500,
                    royalties: 300,
                    month: 'Jan',
                    year: '2024',
                    values: [800, 400, 300],
                    labels: ['US', 'UK', 'DE']
                },
                {
                    sales: 2200,
                    royalties: 450,
                    month: 'Feb',
                    year: '2024',
                    values: [1200, 600, 400],
                    labels: ['US', 'UK', 'DE']
                },
                {
                    sales: 1800,
                    royalties: 360,
                    month: 'Mar',
                    year: '2024',
                    values: [1000, 500, 300],
                    labels: ['US', 'UK', 'DE']
                }
            ],
            options: {
                compareMode: true,
                compareData: [
                    {
                        sales: 1200,
                        royalties: 240,
                        month: 'Jan',
                        year: '2023',
                        values: [600, 300, 300],
                        labels: ['US', 'UK', 'DE']
                    },
                    {
                        sales: 1800,
                        royalties: 360,
                        month: 'Feb',
                        year: '2023',
                        values: [900, 500, 400],
                        labels: ['US', 'UK', 'DE']
                    },
                    {
                        sales: 1400,
                        royalties: 280,
                        month: 'Mar',
                        year: '2023',
                        values: [700, 400, 300],
                        labels: ['US', 'UK', 'DE']
                    }
                ]
            }
        });

        // Test 2: Monthly Sales with Last Week Comparison
        const chart2 = new SnapChart({
            container: document.getElementById('chart2'),
            type: 'stacked-column',
            data: [
                {
                    sales: 2500,
                    royalties: 500,
                    month: 'Current',
                    year: '2024',
                    values: [1500, 700, 300],
                    labels: ['US', 'UK', 'DE']
                }
            ],
            options: {
                compareMode: true,
                compareData: [
                    {
                        sales: 1800,
                        royalties: 360,
                        month: 'Last Week',
                        year: '2024',
                        values: [1000, 500, 300],
                        labels: ['US', 'UK', 'DE']
                    }
                ]
            }
        });

        // Test 3: Stacked Column without Comparison
        const chart3 = new SnapChart({
            container: document.getElementById('chart3'),
            type: 'stacked-column',
            data: [
                {
                    sales: 1200,
                    royalties: 240,
                    month: 'Jan',
                    year: '2024',
                    values: [600, 300, 300],
                    labels: ['US', 'UK', 'DE']
                },
                {
                    sales: 1600,
                    royalties: 320,
                    month: 'Feb',
                    year: '2024',
                    values: [800, 500, 300],
                    labels: ['US', 'UK', 'DE']
                }
            ]
        });

        console.log('Column positioning test loaded successfully');
        console.log('Expected behavior: Comparison columns should appear on the left side of main columns');
    </script>
</body>
</html> 