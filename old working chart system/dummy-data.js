/**
 * ============================================================================
 * SNAP CHARTS - Dummy Data Module
 * ============================================================================
 * 
 * External test data for chart components to avoid hardcoded data.
 * Converted to JavaScript module to avoid CORS issues with file:// URLs.
 * 
 * @version 1.0.0
 * <AUTHOR> for MOD Team
 */

window.SnapDummyData = {
  "templates": {
    "daily-sales-history": {
      "structure": {
        "month": "MMM",
        "day": "DD",
        "year": "YY",
        "sales": 0,
        "royalties": 0,
        "returns": 0,
        "fullDate": "YYYY-MM-DD",
        "dateObj": "YYYY-MM-DDTHH:mm:ss.sssZ"
      },
      "sample": []
    },
    "stacked-column": {
      "structure": {
        "month": "MMM",
        "day": "DD",
        "year": "YY",
        "marketplaces": [
          {
            "code": "US",
            "sales": 0,
            "royalties": 0,
            "returns": 0
          }
        ],
        "sales": 0,
        "royalties": 0,
        "returns": 0,
        "values": [],
        "labels": []
      },
      "sample": []
    },
    "scrollable-stacked-column": {
      "structure": {
        "month": "MMM",
        "year": "YY",
        "marketplaces": [
          {
            "code": "US",
            "sales": 0,
            "royalties": 0,
            "returns": 0
          }
        ],
        "sales": 0,
        "royalties": 0,
        "returns": 0,
        "values": [],
        "labels": []
      },
      "sample": []
    },
    "pie": {
      "structure": {
        "label": "string",
        "value": 0,
        "percentage": 0
      },
      "sample": []
    }
  },
  "testData": {
    "stackedColumn": [
      {
        "month": "OCT",
        "day": "15",
        "year": "24",
        "marketplaces": [
          { "code": "US", "sales": 40, "royalties": 14, "returns": 4 },
          { "code": "UK", "sales": 35, "royalties": 12, "returns": 0 },
          { "code": "DE", "sales": 50, "royalties": 18, "returns": 5 }
        ],
        "sales": 125,
        "royalties": 44,
        "returns": 9,
        "values": [40, 35, 50],
        "labels": ["US", "UK", "DE"]
      },
      {
        "month": "OCT",
        "day": "16", 
        "year": "24",
        "marketplaces": [
          { "code": "US", "sales": 30, "royalties": 10, "returns": 0 },
          { "code": "UK", "sales": 28, "royalties": 10, "returns": 3 },
          { "code": "DE", "sales": 40, "royalties": 14, "returns": 4 }
        ],
        "sales": 98,
        "royalties": 34,
        "returns": 7,
        "values": [30, 28, 40],
        "labels": ["US", "UK", "DE"]
      }
    ],
    "pieCharts": {
      "fitType": [
        { "label": "Women", "value": 1245 },
        { "label": "Men", "value": 1867 },
        { "label": "Unisex", "value": 2134 },
        { "label": "Youth", "value": 876 },
        { "label": "Girls", "value": 543 }
      ],
      "soldColors": [
        { "label": "Black", "value": 3245, "color": "#000000" },
        { "label": "White", "value": 2876, "color": "#FFFFFF" },
        { "label": "Navy", "value": 2134, "color": "#000080" },
        { "label": "Heather Grey", "value": 1876, "color": "#A8A8A8" },
        { "label": "Dark Heather", "value": 1654, "color": "#414141" },
        { "label": "Royal", "value": 1234, "color": "#4169E1" },
        { "label": "Red", "value": 1123, "color": "#DC143C" },
        { "label": "Purple", "value": 987, "color": "#800080" },
        { "label": "Kelly Green", "value": 876, "color": "#228B22" },
        { "label": "Pink", "value": 765, "color": "#FFC0CB" },
        { "label": "Asphalt", "value": 654, "color": "#2F4F4F" },
        { "label": "Baby Blue", "value": 543, "color": "#87CEEB" },
        { "label": "Cranberry", "value": 432, "color": "#DC143C" },
        { "label": "Grass", "value": 398, "color": "#7CFC00" },
        { "label": "Heather Blue", "value": 365, "color": "#6495ED" },
        { "label": "Olive", "value": 321, "color": "#808000" },
        { "label": "Orange", "value": 298, "color": "#FFA500" },
        { "label": "Slate", "value": 276, "color": "#708090" },
        { "label": "Brown", "value": 254, "color": "#A52A2A" },
        { "label": "Silver", "value": 232, "color": "#C0C0C0" },
        { "label": "Burgundy", "value": 210, "color": "#800020" },
        { "label": "Dark Green", "value": 189, "color": "#006400" },
        { "label": "Golden Yellow", "value": 167, "color": "#FFD700" },
        { "label": "Lemon", "value": 145, "color": "#FFFACD" },
        { "label": "Sapphire", "value": 123, "color": "#0F52BA" },
        { "label": "Forest Green", "value": 101, "color": "#228B22" },
        { "label": "Purple Heather", "value": 89, "color": "#9370DB" },
        { "label": "Red Heather", "value": 78, "color": "#CD5C5C" },
        { "label": "Olive Heather", "value": 67, "color": "#9ACD32" },
        { "label": "Pink Heather", "value": 56, "color": "#DDA0DD" },
        { "label": "Neon Pink", "value": 45, "color": "#FF1493" },
        { "label": "Sage Green", "value": 34, "color": "#87A96B" },
        { "label": "Bright Pink", "value": 28, "color": "#FF69B4" },
        { "label": "Dusty Blue", "value": 23, "color": "#6B8CAE" },
        { "label": "Brushed Steel", "value": 18, "color": "#A8A8A8" },
        { "label": "Black White", "value": 15, "color": "#000000" },
        { "label": "Navy White", "value": 12, "color": "#000080" },
        { "label": "Red White", "value": 10, "color": "#DC143C" },
        { "label": "Royal Blue White", "value": 8, "color": "#4169E1" },
        { "label": "Dark Heather White", "value": 6, "color": "#414141" },
        { "label": "Black Athletic Heather", "value": 5, "color": "#2F2F2F" },
        { "label": "Navy Athletic Heather", "value": 3, "color": "#1C1C3A" }
      ],
      "topProducts": [
        { "label": "S. T-Shirt", "value": 3245, "percentage": 28.5 },
        { "label": "P. T-Shirt", "value": 2876, "percentage": 25.3 },
        { "label": "Hoodie", "value": 1654, "percentage": 14.5 },
        { "label": "Sweatshirt", "value": 1234, "percentage": 10.8 },
        { "label": "V-Neck", "value": 987, "percentage": 8.7 },
        { "label": "Tank Top", "value": 765, "percentage": 6.7 },
        { "label": "Mug", "value": 432, "percentage": 3.8 },
        { "label": "Tote Bag", "value": 298, "percentage": 2.6 },
        { "label": "Throw Pillow", "value": 234, "percentage": 2.1 },
        { "label": "Phone Case", "value": 189, "percentage": 1.7 },
        { "label": "Pop Socket", "value": 156, "percentage": 1.4 },
        { "label": "Poster", "value": 123, "percentage": 1.1 },
        { "label": "Sticker", "value": 98, "percentage": 0.9 },
        { "label": "Apron", "value": 87, "percentage": 0.8 },
        { "label": "Dress", "value": 67, "percentage": 0.6 }
      ],
      "returnRate": [
        { "label": "Dress", "value": 15.2, "percentage": 15.2 },
        { "label": "Apron", "value": 12.8, "percentage": 12.8 },
        { "label": "Hoodie", "value": 8.9, "percentage": 8.9 },
        { "label": "Sweatshirt", "value": 7.6, "percentage": 7.6 },
        { "label": "Phone Case", "value": 6.3, "percentage": 6.3 },
        { "label": "Tote Bag", "value": 5.1, "percentage": 5.1 },
        { "label": "V-Neck", "value": 4.8, "percentage": 4.8 },
        { "label": "P. T-Shirt", "value": 4.2, "percentage": 4.2 },
        { "label": "S. T-Shirt", "value": 3.9, "percentage": 3.9 },
        { "label": "Tank Top", "value": 3.5, "percentage": 3.5 },
        { "label": "Throw Pillow", "value": 2.8, "percentage": 2.8 },
        { "label": "Mug", "value": 2.1, "percentage": 2.1 },
        { "label": "Poster", "value": 1.6, "percentage": 1.6 },
        { "label": "Pop Socket", "value": 1.2, "percentage": 1.2 },
        { "label": "Sticker", "value": 0.8, "percentage": 0.8 }
      ],
      "soldSizes": [
        { "label": "L", "value": 3245, "percentage": 24.8 },
        { "label": "M", "value": 2876, "percentage": 22.0 },
        { "label": "XL", "value": 2134, "percentage": 16.3 },
        { "label": "S", "value": 1876, "percentage": 14.3 },
        { "label": "2XL", "value": 1234, "percentage": 9.4 },
        { "label": "3XL", "value": 876, "percentage": 6.7 },
        { "label": "XS", "value": 654, "percentage": 5.0 },
        { "label": "4XL", "value": 198, "percentage": 1.5 }
      ],
      "salesByMarketplace": [
        { "label": "US", "value": 4567, "percentage": 45.2 },
        { "label": "UK", "value": 1234, "percentage": 12.2 },
        { "label": "DE", "value": 1156, "percentage": 11.4 },
        { "label": "FR", "value": 876, "percentage": 8.7 },
        { "label": "IT", "value": 654, "percentage": 6.5 },
        { "label": "ES", "value": 543, "percentage": 5.4 },
        { "label": "JP", "value": 321, "percentage": 3.2 }
      ],
      "demographics": [
        { "label": "Male", "value": 144, "percentage": 13 },
        { "label": "Female", "value": 256, "percentage": 23 },
        { "label": "Unisex", "value": 198, "percentage": 18 },
        { "label": "Youth", "value": 167, "percentage": 15 },
        { "label": "Kids", "value": 123, "percentage": 11 },
        { "label": "Adult", "value": 189, "percentage": 17 },
        { "label": "Other", "value": 33, "percentage": 3 }
      ]
    },
    "animationTest": [
      {
        "month": "JAN",
        "day": "01",
        "year": "24",
        "sales": 245,
        "royalties": 98,
        "values": [80, 65, 100],
        "labels": ["US", "UK", "DE"]
      },
      {
        "month": "JAN",
        "day": "02",
        "year": "24",
        "sales": 189,
        "royalties": 76,
        "values": [60, 49, 80],
        "labels": ["US", "UK", "DE"]
      },
      {
        "month": "JAN",
        "day": "03",
        "year": "24",
        "sales": 312,
        "royalties": 125,
        "values": [100, 82, 130],
        "labels": ["US", "UK", "DE"]
      }
    ],
    "dailySalesSync": [
      {
        "month": "DEC",
        "day": "28",
        "year": "24",
        "sales": 1277,
        "royalties": 537,
        "fullDate": "2024-12-27",
        "dateObj": "2024-12-27T22:00:00.000Z"
      }
    ]
  },
  "generators": {
    "dailySalesHistory": {
      "description": "Generates daily sales data for a specified date range",
      "parameters": {
        "startDate": "2015-01-01",
        "endDate": "2024-12-31",
        "salesRange": { "min": 1, "max": 50 },
        "royaltiesMultiplier": 0.4,
        "zeroSalesProbability": 0.30,
        "consecutiveZeroWeeks": 1
      }
    },
    "stackedColumn": {
      "description": "Generates stacked column data with marketplace breakdown",
      "parameters": {
        "dateRange": 30,
        "marketplaces": ["US", "UK", "DE", "FR", "IT", "ES", "JP"],
        "salesRange": { "min": 50, "max": 500 },
        "royaltiesMultiplier": 0.35
      }
    }
  }
}; 