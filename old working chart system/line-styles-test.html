<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Snap Charts - Line Styles Comparison</title>
    
    <!-- Load existing project fonts and CSS variables -->
    <link rel="stylesheet" href="../../snapapp.css">
    
    <!-- Load chart-specific CSS -->
    <link rel="stylesheet" href="snap-charts.css">
    
    <style>
        /* Test page specific styles */
        html, body, .test-container, .charts-grid {
            height: auto !important;
            min-height: unset !important;
            overflow: unset !important;
        }
        
        body {
            margin: 0;
            padding: 40px;
            background: var(--bg-secondary);
            color: var(--text-primary);
            font-family: 'Amazon Ember', Arial, sans-serif;
        }
        
        .test-container {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            gap: 32px;
            width: 100%;
            box-sizing: border-box;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 32px;
        }
        
        .test-title {
            font-size: 32px;
            font-weight: 700;
            color: var(--text-accent);
            margin: 0 0 8px 0;
        }
        
        .test-subtitle {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
            margin: 0;
        }
        
        .controls-section {
            display: flex;
            justify-content: center;
            gap: 16px;
            margin-bottom: 32px;
        }
        
        .control-btn {
            padding: 8px 16px;
            border: 1.5px solid var(--border-color);
            border-radius: 6px;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-family: 'Amazon Ember', sans-serif;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--theme-transition);
        }
        
        .control-btn:hover {
            background: var(--btn-hover);
            border-color: var(--action-btn-bg);
        }
        
        .control-btn.active {
            background: var(--action-btn-bg);
            color: var(--action-btn-text);
            border-color: var(--action-btn-bg);
        }
        
        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 32px;
            margin-bottom: 32px;
            width: 100%;
            max-width: 100%;
        }
        
        .chart-container {
            background: var(--bg-primary);
            border: 1.5px solid var(--border-color);
            border-radius: 14px;
            padding: 24px;
            position: static;
        }
        
        .chart-title {
            font-size: 18px;
            font-weight: 700;
            color: var(--text-accent);
            margin: 0 0 16px 0;
            text-align: center;
        }
        
        .chart-description {
            font-size: 14px;
            color: var(--text-secondary);
            margin: 0 0 20px 0;
            text-align: center;
            line-height: 1.4;
        }
        
        .chart-wrapper {
            width: 100%;
            height: 300px;
            position: static;
            overflow: visible;
        }
        
        .demo-info {
            background: var(--bg-primary);
            border: 1.5px solid var(--border-color);
            border-radius: 14px;
            padding: 24px;
            margin-top: 32px;
        }
        
        .demo-info h3 {
            margin: 0 0 16px 0;
            font-size: 18px;
            font-weight: 700;
            color: var(--text-accent);
        }
        
        .demo-info p {
            margin: 0 0 12px 0;
            font-size: 14px;
            line-height: 1.5;
            color: var(--text-primary);
        }
        
        .demo-info ul {
            margin: 12px 0;
            padding-left: 20px;
        }
        
        .demo-info li {
            margin: 8px 0;
            font-size: 14px;
            color: var(--text-primary);
        }
        
        .style-comparison {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin: 20px 0;
        }
        
        .style-card {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 16px;
            text-align: center;
        }
        
        .style-name {
            font-weight: 700;
            color: var(--text-accent);
            margin-bottom: 8px;
        }
        
        .style-description {
            font-size: 12px;
            color: var(--text-secondary);
            line-height: 1.3;
        }
        
        @media (max-width: 768px) {
            body {
                padding: 20px;
                overflow: auto;
            }
            
            .charts-grid {
                grid-template-columns: 1fr;
            }
            
            .controls-section {
                flex-wrap: wrap;
            }
            
            .test-title {
                font-size: 24px;
            }
            
            .chart-wrapper {
                height: 250px;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <!-- Header -->
        <div class="test-header">
            <h1 class="test-title">Snap Charts - Line Styles Comparison</h1>
            <p class="test-subtitle">Compare different line styles for royalties trend visualization</p>
        </div>
        
        <!-- Controls -->
        <div class="controls-section">
            <button class="control-btn active" id="lightTheme">Light Theme</button>
            <button class="control-btn" id="darkTheme">Dark Theme</button>
            <button class="control-btn" id="updateData">Update Data</button>
            <button class="control-btn" id="toggleAnimation">Toggle Animation</button>
            <button class="control-btn active" id="toggleCompare">Compare Mode: ON</button>
        </div>
        
        <!-- Charts Grid -->
        <div class="charts-grid">
            <!-- Curvy/Smooth Style -->
            <div class="chart-container">
                <h3 class="chart-title">Curvy/Smooth (Current)</h3>
                <p class="chart-description">Cubic Bezier curves with Catmull-Rom interpolation. Elegant and organic for trend visualization.</p>
                <div class="chart-wrapper" id="curvyChart"></div>
            </div>
            
            <!-- Sharp/Linear Style -->
            <div class="chart-container">
                <h3 class="chart-title">Sharp/Linear</h3>
                <p class="chart-description">Straight lines between points. Precise and technical, shows exact data transitions.</p>
                <div class="chart-wrapper" id="sharpChart"></div>
            </div>
            
            <!-- Stepped/Staircase Style -->
            <div class="chart-container">
                <h3 class="chart-title">Stepped/Staircase</h3>
                <p class="chart-description">Horizontal then vertical segments. Perfect for discrete value changes.</p>
                <div class="chart-wrapper" id="steppedChart"></div>
            </div>
            
            <!-- Monotone Cubic Style -->
            <div class="chart-container">
                <h3 class="chart-title">Monotone Cubic</h3>
                <p class="chart-description">Smooth curves that prevent overshooting between data points. Controlled smoothness.</p>
                <div class="chart-wrapper" id="monotoneChart"></div>
            </div>
            
            <!-- Cardinal Spline Style -->
            <div class="chart-container">
                <h3 class="chart-title">Cardinal Spline</h3>
                <p class="chart-description">Smooth curves with adjustable tension. Customizable curve tightness.</p>
                <div class="chart-wrapper" id="cardinalChart"></div>
            </div>
            
            <!-- Sharp with Rounded Joints Style -->
            <div class="chart-container">
                <h3 class="chart-title">Sharp with Rounded Joints</h3>
                <p class="chart-description">Straight line segments with smooth curved connections at data points.</p>
                <div class="chart-wrapper" id="sharpRoundedChart"></div>
            </div>
            
            <!-- Basis Spline Style -->
            <div class="chart-container">
                <h3 class="chart-title">Basis Spline</h3>
                <p class="chart-description">B-spline curves for extremely smooth, mathematically precise lines.</p>
                <div class="chart-wrapper" id="basisChart"></div>
            </div>
        </div>
        
        <!-- Style Comparison Guide -->
        <div class="demo-info">
            <h3>🎨 Line Style Comparison Guide</h3>
            <p>Each line style serves different purposes and visual preferences:</p>
            
            <div class="style-comparison">
                <div class="style-card">
                    <div class="style-name">Curvy/Smooth</div>
                    <div class="style-description">Best for: Trend visualization, elegant presentations, organic data flow</div>
                </div>
                <div class="style-card">
                    <div class="style-name">Sharp/Linear</div>
                    <div class="style-description">Best for: Technical data, precise measurements, clear transitions</div>
                </div>
                <div class="style-card">
                    <div class="style-name">Sharp Rounded</div>
                    <div class="style-description">Best for: Precise data with elegant joints, hybrid precision/smoothness</div>
                </div>
                <div class="style-card">
                    <div class="style-name">Stepped</div>
                    <div class="style-description">Best for: Discrete values, step functions, categorical changes</div>
                </div>
                <div class="style-card">
                    <div class="style-name">Monotone Cubic</div>
                    <div class="style-description">Best for: Smooth but constrained curves, no overshooting</div>
                </div>
                <div class="style-card">
                    <div class="style-name">Cardinal Spline</div>
                    <div class="style-description">Best for: Customizable smoothness, adjustable tension</div>
                </div>
                <div class="style-card">
                    <div class="style-name">Basis Spline</div>
                    <div class="style-description">Best for: Maximum smoothness, mathematical precision</div>
                </div>
            </div>
            
            <h3>📊 Usage Recommendations</h3>
            <ul>
                <li><strong>Financial Data:</strong> Curvy/Smooth or Monotone Cubic for trend analysis</li>
                <li><strong>Technical Metrics:</strong> Sharp/Linear for precise measurements</li>
                <li><strong>Hybrid Precision:</strong> Sharp Rounded for professional dashboards</li>
                <li><strong>Status Changes:</strong> Stepped for discrete state transitions</li>
                <li><strong>Sales Trends:</strong> Cardinal Spline with medium tension</li>
                <li><strong>Smooth Animations:</strong> Basis Spline for fluid motion</li>
            </ul>
            
            <h3>🔄 Comparison Mode Features</h3>
            <ul>
                <li><strong>Dual Lines:</strong> Main data (solid) vs comparison data (dashed)</li>
                <li><strong>Different Colors:</strong> Easy visual distinction between datasets</li>
                <li><strong>Higher Values:</strong> Comparison data includes peaks that exceed main data</li>
                <li><strong>Auto Scaling:</strong> Y-axis adjusts to accommodate both datasets</li>
                <li><strong>All Styles:</strong> Every line style supports comparison mode</li>
            </ul>
        </div>
    </div>

    <!-- Load Chart JavaScript -->
    <script src="snap-charts.js"></script>
    
    <script>
        // Enhanced SnapChart class with multiple line styles
        class SnapChartWithLineStyles extends SnapChart {
            constructor(options = {}) {
                super(options);
                this.lineStyle = options.lineStyle || 'curvy';
            }
            
            // Override the drawRoyaltiesLine method to support multiple styles
            drawRoyaltiesLine(parent, points, isComparison = false) {
                if (points.length < 2) return;
                
                let pathData = '';
                
                switch (this.lineStyle) {
                    case 'sharp':
                        pathData = this.createSharpPath(points);
                        break;
                    case 'sharp-rounded':
                        pathData = this.createSharpRoundedPath(points);
                        break;
                    case 'stepped':
                        pathData = this.createSteppedPath(points);
                        break;
                    case 'monotone':
                        pathData = this.createMonotonePath(points);
                        break;
                    case 'cardinal':
                        pathData = this.createCardinalPath(points);
                        break;
                    case 'basis':
                        pathData = this.createBasisPath(points);
                        break;
                    case 'curvy':
                    default:
                        pathData = this.createCurvyPath(points);
                        break;
                }
                
                // Draw the path
                const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                path.setAttribute('d', pathData);
                path.classList.add('snap-chart-royalties-line');
                
                if (isComparison) {
                    path.classList.add('snap-chart-comparison-line');
                }
                
                parent.appendChild(path);
                
                // Add dots (only for main data)
                if (!isComparison) {
                    points.forEach((point, index) => {
                        const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
                        circle.setAttribute('cx', point.x);
                        circle.setAttribute('cy', point.y);
                        circle.setAttribute('r', '3');
                        circle.classList.add('snap-chart-royalties-dot');
                        circle.setAttribute('data-column-index', point.index);
                        parent.appendChild(circle);
                    });
                }
            }
            
            // Curvy/Smooth path (current implementation)
            createCurvyPath(points) {
                let pathData = `M ${points[0].x} ${points[0].y}`;
                for (let i = 0; i < points.length - 1; i++) {
                    const p0 = points[i - 1] || points[i];
                    const p1 = points[i];
                    const p2 = points[i + 1];
                    const p3 = points[i + 2] || p2;
                    
                    const cp1x = p1.x + (p2.x - p0.x) / 6;
                    const cp1y = p1.y + (p2.y - p0.y) / 6;
                    const cp2x = p2.x - (p3.x - p1.x) / 6;
                    const cp2y = p2.y - (p3.y - p1.y) / 6;
                    
                    pathData += ` C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${p2.x} ${p2.y}`;
                }
                return pathData;
            }
            
            // Sharp/Linear path
            createSharpPath(points) {
                let pathData = `M ${points[0].x} ${points[0].y}`;
                for (let i = 1; i < points.length; i++) {
                    pathData += ` L ${points[i].x} ${points[i].y}`;
                }
                return pathData;
            }
            
            // Stepped/Staircase path
            createSteppedPath(points) {
                let pathData = `M ${points[0].x} ${points[0].y}`;
                for (let i = 1; i < points.length; i++) {
                    const prevPoint = points[i - 1];
                    const currPoint = points[i];
                    // Horizontal then vertical
                    pathData += ` L ${currPoint.x} ${prevPoint.y}`;
                    pathData += ` L ${currPoint.x} ${currPoint.y}`;
                }
                return pathData;
            }
            
            // Monotone Cubic path (simplified version)
            createMonotonePath(points) {
                let pathData = `M ${points[0].x} ${points[0].y}`;
                for (let i = 0; i < points.length - 1; i++) {
                    const p1 = points[i];
                    const p2 = points[i + 1];
                    
                    // Simple monotone cubic with constrained control points
                    const dx = p2.x - p1.x;
                    const dy = p2.y - p1.y;
                    
                    const cp1x = p1.x + dx * 0.3;
                    const cp1y = p1.y + dy * 0.3;
                    const cp2x = p2.x - dx * 0.3;
                    const cp2y = p2.y - dy * 0.3;
                    
                    pathData += ` C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${p2.x} ${p2.y}`;
                }
                return pathData;
            }
            
            // Cardinal Spline path
            createCardinalPath(points) {
                const tension = 0.5; // Adjustable tension
                let pathData = `M ${points[0].x} ${points[0].y}`;
                
                for (let i = 0; i < points.length - 1; i++) {
                    const p0 = points[i - 1] || points[i];
                    const p1 = points[i];
                    const p2 = points[i + 1];
                    const p3 = points[i + 2] || p2;
                    
                    const cp1x = p1.x + (p2.x - p0.x) * tension / 3;
                    const cp1y = p1.y + (p2.y - p0.y) * tension / 3;
                    const cp2x = p2.x - (p3.x - p1.x) * tension / 3;
                    const cp2y = p2.y - (p3.y - p1.y) * tension / 3;
                    
                    pathData += ` C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${p2.x} ${p2.y}`;
                }
                return pathData;
            }
            
            // Basis Spline path (simplified B-spline)
            createBasisPath(points) {
                let pathData = `M ${points[0].x} ${points[0].y}`;
                
                for (let i = 0; i < points.length - 1; i++) {
                    const p1 = points[i];
                    const p2 = points[i + 1];
                    
                    // B-spline approximation with very smooth curves
                    const dx = p2.x - p1.x;
                    const dy = p2.y - p1.y;
                    
                    const cp1x = p1.x + dx * 0.25;
                    const cp1y = p1.y + dy * 0.1;
                    const cp2x = p2.x - dx * 0.25;
                    const cp2y = p2.y - dy * 0.1;
                    
                    pathData += ` C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${p2.x} ${p2.y}`;
                }
                return pathData;
            }
            
            // Sharp with Rounded Joints path (straight lines with curved connections)
            createSharpRoundedPath(points) {
                if (points.length < 2) return '';
                
                const cornerRadius = 8; // Adjustable corner radius
                let pathData = `M ${points[0].x} ${points[0].y}`;
                
                for (let i = 1; i < points.length; i++) {
                    const prevPoint = points[i - 1];
                    const currPoint = points[i];
                    const nextPoint = points[i + 1];
                    
                    if (i === points.length - 1) {
                        // Last point - just draw straight line
                        pathData += ` L ${currPoint.x} ${currPoint.y}`;
                    } else {
                        // Calculate vectors for incoming and outgoing lines
                        const incomingDx = currPoint.x - prevPoint.x;
                        const incomingDy = currPoint.y - prevPoint.y;
                        const incomingLength = Math.sqrt(incomingDx * incomingDx + incomingDy * incomingDy);
                        
                        const outgoingDx = nextPoint.x - currPoint.x;
                        const outgoingDy = nextPoint.y - currPoint.y;
                        const outgoingLength = Math.sqrt(outgoingDx * outgoingDx + outgoingDy * outgoingDy);
                        
                        // Normalize vectors
                        const incomingUnitX = incomingDx / incomingLength;
                        const incomingUnitY = incomingDy / incomingLength;
                        const outgoingUnitX = outgoingDx / outgoingLength;
                        const outgoingUnitY = outgoingDy / outgoingLength;
                        
                        // Calculate the radius based on available space
                        const maxRadius = Math.min(cornerRadius, incomingLength / 2, outgoingLength / 2);
                        
                        // Calculate start and end points of the curve
                        const curveStartX = currPoint.x - incomingUnitX * maxRadius;
                        const curveStartY = currPoint.y - incomingUnitY * maxRadius;
                        const curveEndX = currPoint.x + outgoingUnitX * maxRadius;
                        const curveEndY = currPoint.y + outgoingUnitY * maxRadius;
                        
                        // Draw straight line to curve start
                        pathData += ` L ${curveStartX} ${curveStartY}`;
                        
                        // Draw quadratic curve through the corner
                        pathData += ` Q ${currPoint.x} ${currPoint.y}, ${curveEndX} ${curveEndY}`;
                    }
                }
                
                return pathData;
            }
        }
        
        // Sample data for testing
        const testData = Array.from({length: 10}, (_, i) => {
            const day = (i + 1).toString().padStart(2, '0');
            const randomFactors = [0.8, 0.2, 0.9, 0.3, 0.7, 0.1, 0.95, 0.4, 0.6, 0.85];
            const sales = Math.round(50 + (200 * randomFactors[i]));
            const royalties = Math.round(sales * (0.4 + Math.random() * 0.8));
            
            return {
                month: 'JAN',
                day,
                sales,
                royalties,
                values: [sales],
                labels: ['US']
            };
        });
        
        // Comparison data with some higher peaks
        const comparisonData = Array.from({length: 10}, (_, i) => {
            const day = (i + 1).toString().padStart(2, '0');
            const randomFactors = [0.6, 0.4, 0.7, 0.2, 0.9, 0.3, 0.8, 0.5, 0.95, 0.65];
            let sales = Math.round(40 + (180 * randomFactors[i]));
            
            // Make specific comparison points higher than main data
            if (i === 3) {
                sales = Math.round(280 + (Math.random() * 40)); // Higher peak
            } else if (i === 7) {
                sales = Math.round(300 + (Math.random() * 50)); // Another high peak
            }
            
            const royalties = Math.round(sales * (0.3 + Math.random() * 0.9));
            
            return {
                month: 'DEC',
                day,
                sales,
                royalties,
                values: [sales],
                labels: ['US']
            };
        });
        
        // Chart configurations
        const chartConfigs = [
            { container: '#curvyChart', lineStyle: 'curvy', title: 'Curvy/Smooth Style' },
            { container: '#sharpChart', lineStyle: 'sharp', title: 'Sharp/Linear Style' },
            { container: '#sharpRoundedChart', lineStyle: 'sharp-rounded', title: 'Sharp with Rounded Joints Style' },
            { container: '#steppedChart', lineStyle: 'stepped', title: 'Stepped/Staircase Style' },
            { container: '#monotoneChart', lineStyle: 'monotone', title: 'Monotone Cubic Style' },
            { container: '#cardinalChart', lineStyle: 'cardinal', title: 'Cardinal Spline Style' },
            { container: '#basisChart', lineStyle: 'basis', title: 'Basis Spline Style' }
        ];
        
        // Create charts with comparison mode enabled
        const charts = chartConfigs.map(config => {
            return new SnapChartWithLineStyles({
                container: config.container,
                type: 'stacked-column',
                data: testData,
                lineStyle: config.lineStyle,
                options: {
                    title: config.title,
                    subtitle: 'Line Style with Comparison Data',
                    animate: false,
                    responsive: true,
                    height: 280,
                    compareMode: true,
                    compareData: comparisonData
                }
            });
        });
        
        // Theme controls
        document.getElementById('lightTheme').addEventListener('click', function() {
            document.documentElement.setAttribute('data-theme', 'light');
            document.querySelectorAll('.control-btn').forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
        });
        
        document.getElementById('darkTheme').addEventListener('click', function() {
            document.documentElement.setAttribute('data-theme', 'dark');
            document.querySelectorAll('.control-btn').forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
        });
        
        // Data update control
        let useAlternateData = false;
        document.getElementById('updateData').addEventListener('click', function() {
            const alternateData = Array.from({length: 10}, (_, i) => {
                const day = (i + 1).toString().padStart(2, '0');
                const randomFactors = [0.3, 0.9, 0.1, 0.8, 0.4, 0.95, 0.2, 0.7, 0.5, 0.6];
                const sales = Math.round(30 + (250 * randomFactors[i]));
                const royalties = Math.round(sales * (0.3 + Math.random() * 1.0));
                
                return {
                    month: 'FEB',
                    day,
                    sales,
                    royalties,
                    values: [sales],
                    labels: ['US']
                };
            });
            
            const dataToUse = useAlternateData ? testData : alternateData;
            useAlternateData = !useAlternateData;
            
            charts.forEach(chart => {
                chart.updateData(dataToUse);
            });
        });
        
        // Animation toggle
        let animationEnabled = false;
        document.getElementById('toggleAnimation').addEventListener('click', function() {
            animationEnabled = !animationEnabled;
            this.textContent = animationEnabled ? 'Disable Animation' : 'Enable Animation';
            
            charts.forEach(chart => {
                chart.options.animate = animationEnabled;
                chart.render();
            });
        });
        
        // Compare mode toggle
        let compareEnabled = true;
        document.getElementById('toggleCompare').addEventListener('click', function() {
            compareEnabled = !compareEnabled;
            this.textContent = compareEnabled ? 'Compare Mode: ON' : 'Compare Mode: OFF';
            
            // Update button styling
            if (compareEnabled) {
                this.classList.add('active');
            } else {
                this.classList.remove('active');
            }
            
            charts.forEach(chart => {
                chart.options.compareMode = compareEnabled;
                chart.options.compareData = compareEnabled ? comparisonData : null;
                chart.render();
            });
        });
    </script>

    <!-- TEMPORARY DEBUG BLOCK FOR SCROLLING -->
    <div style="height: 1200px; background: repeating-linear-gradient(45deg, #eee, #ddd 20px, #eee 40px); text-align:center; font-size:2em; color:#888;">
        <br><br>SCROLL TEST BLOCK<br><br>
        If you can see this, scrolling works!<br><br>
        (Remove this block after confirming scrolling is fixed)
    </div>
</body>
</html> 