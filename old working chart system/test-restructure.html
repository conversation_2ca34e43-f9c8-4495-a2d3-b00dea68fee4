<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chart Structure Test</title>
    <link rel="stylesheet" href="snap-charts.css">
    <style>
        body {
            font-family: 'Amazon Ember', Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Chart Structure Test</h1>
        <div id="chart-container"></div>
    </div>

    <script src="dummy-data.js"></script>
    <script src="snap-charts.js"></script>
    <script>
        // Test the new structure
        const chartContainer = document.getElementById('chart-container');
        
        const chart = new SnapChart({
            container: chartContainer,
            type: 'stacked-column',
            title: 'Sales Performance',
            subtitle: 'Monthly breakdown by marketplace',
            icon: `<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                     <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
                   </svg>`,
            button: {
                text: 'Export',
                icon: `<svg width="8" height="8" viewBox="0 0 24 24" fill="currentColor">
                         <path d="M19 9h-4V3H9v6H5l7 7 7-7z"/>
                       </svg>`,
                onClick: () => console.log('Export clicked')
            },
            data: dummyStackedColumnData
        });
        
        console.log('Chart created with new structure');
        console.log('Title section should be above chart container');
        console.log('Data editor should be below chart container');
    </script>
</body>
</html> 