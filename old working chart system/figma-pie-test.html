<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>4 Pie Charts Dashboard</title>
    <style>
        :root {
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --text-primary: #1a1a1a;
            --text-secondary: #606f95;
            --border-color: #e5e7eb;
            --border-hover: #d1d5db;
            --btn-hover: #f3f4f6;
            --tooltip-bg: #000000;
            --tooltip-text: #ffffff;
            --theme-transition: all 0.2s ease;
        }

        [data-theme="dark"] {
            --bg-primary: #1e2028;
            --bg-secondary: #292e38;
            --text-primary: #ffffff;
            --text-secondary: #b4b9c5;
            --border-color: #3a3f4a;
            --border-hover: #4a5063;
            --btn-hover: #353b47;
            --tooltip-bg: #000000;
            --tooltip-text: #ffffff;
        }

        body {
            font-family: 'Amazon Ember', Arial, sans-serif;
            background: var(--bg-secondary);
            color: var(--text-primary);
            margin: 0;
            padding: 40px;
            transition: var(--theme-transition);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            color: var(--text-primary);
            margin-bottom: 40px;
        }

        .charts-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 24px;
            margin-bottom: 32px;
        }

        .chart-container {
            background: var(--bg-primary);
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: var(--theme-transition);
        }

        .chart-container h2 {
            margin: 0 0 16px 0;
            font-size: 16px;
            font-weight: 700;
            color: var(--text-primary);
            text-align: center;
        }

        .controls {
            display: flex;
            gap: 16px;
            justify-content: center;
            margin-bottom: 32px;
        }

        .control-btn {
            padding: 8px 16px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            background: var(--bg-primary);
            color: var(--text-primary);
            cursor: pointer;
            transition: var(--theme-transition);
            font-family: 'Amazon Ember', Arial, sans-serif;
            font-weight: 500;
        }

        .control-btn:hover {
            background: var(--btn-hover);
            border-color: var(--border-hover);
        }

        .control-btn.active {
            background: #8562FF;
            border-color: #8562FF;
            color: white;
        }

        .specs {
            background: var(--bg-primary);
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 32px;
            border: 2px solid var(--border-color);
        }

        .specs h3 {
            margin-top: 0;
            color: var(--text-primary);
        }

        .specs ul {
            color: var(--text-secondary);
            line-height: 1.6;
        }

        .specs code {
            background: var(--bg-secondary);
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
        }

        /* Responsive design */
        @media (max-width: 1400px) {
            .charts-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }
        
        @media (max-width: 1200px) {
            .charts-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .charts-grid {
                grid-template-columns: 1fr;
            }
            
            body {
                padding: 20px;
            }
        }
    </style>
    <link rel="stylesheet" href="snap-charts.css">
</head>
<body data-theme="light">
    <div class="container">
        <h1>📊 5 Pie Charts Dashboard</h1>
        
        <div class="specs">
            <h3>📋 Dashboard Specifications</h3>
            <ul>
                <li><strong>Fit Type:</strong> Women, Men, Unisex, Youth, Girls using standard 7 colors</li>
                <li><strong>Sold Colors:</strong> 30 colors with each slice colored as per the actual color</li>
                <li><strong>Top Products:</strong> Full product list with sales values and percentages</li>
                <li><strong>Returns Rate:</strong> Products with return percentages using color variations</li>
                <li><strong>Sold Sizes:</strong> Size distribution with sales values and percentages</li>
            </ul>
        </div>

        <div class="controls">
            <button id="lightTheme" class="control-btn active">☀️ Light Theme</button>
            <button id="darkTheme" class="control-btn">🌙 Dark Theme</button>
        </div>

        <div class="charts-grid">
            <div class="chart-container" id="fitTypeChart">
                <h2>Fit Type Distribution</h2>
            </div>
            <div class="chart-container" id="soldColorsChart">
                <h2>Sold Colors</h2>
            </div>
            <div class="chart-container" id="topProductsChart">
                <h2>Top Products</h2>
            </div>
            <div class="chart-container" id="returnRateChart">
                <h2>Returns Rate</h2>
            </div>
                    <div class="chart-container" id="soldSizesChart">
            <h2>Sold Sizes</h2>
        </div>
        
        <div class="chart-container" id="salesByMarketplaceChart">
            <h2>Sales by Marketplace</h2>
        </div>
    </div>
    </div>

    <script src="dummy-data.js"></script>
    <script src="data-loader.js"></script>
    <script src="snap-charts.js"></script>
    <script>
        // Main async function to handle data loading
        async function initializePieCharts() {
            const dataLoader = new SnapDataLoader();
            
            // Load all pie chart data from external file
            const fitTypeData = await dataLoader.getPieChartData('fitType');
            const soldColorsData = await dataLoader.getPieChartData('soldColors');
            const topProductsData = await dataLoader.getPieChartData('topProducts');
            const returnRateData = await dataLoader.getPieChartData('returnRate');
            const soldSizesData = await dataLoader.getPieChartData('soldSizes');
            const salesByMarketplaceData = await dataLoader.getPieChartData('salesByMarketplace');
            
            // Legacy hardcoded data (replaced by external data loading)
            /*
            const fitTypeData = [
                { label: 'Women', value: 1245 },
                { label: 'Men', value: 1867 },
                { label: 'Unisex', value: 2134 },
                { label: 'Youth', value: 876 },
                { label: 'Girls', value: 543 }
            ];

        // 2. Sold Colors data - Using actual product colors with realistic sales distribution
        const soldColorsData = [
            // Top selling colors (popular across multiple products)
            { label: 'Black', value: 3245, color: '#000000' },
            { label: 'White', value: 2876, color: '#FFFFFF' },
            { label: 'Navy', value: 2134, color: '#000080' },
            { label: 'Heather Grey', value: 1876, color: '#A8A8A8' },
            { label: 'Dark Heather', value: 1654, color: '#414141' },
            
            // Mid-tier colors (good sellers)
            { label: 'Royal', value: 1234, color: '#4169E1' },
            { label: 'Red', value: 1123, color: '#DC143C' },
            { label: 'Purple', value: 987, color: '#800080' },
            { label: 'Kelly Green', value: 876, color: '#228B22' },
            { label: 'Pink', value: 765, color: '#FFC0CB' },
            
            // Regular colors
            { label: 'Asphalt', value: 654, color: '#2F4F4F' },
            { label: 'Baby Blue', value: 543, color: '#87CEEB' },
            { label: 'Cranberry', value: 432, color: '#DC143C' },
            { label: 'Grass', value: 398, color: '#7CFC00' },
            { label: 'Heather Blue', value: 365, color: '#6495ED' },
            { label: 'Olive', value: 321, color: '#808000' },
            { label: 'Orange', value: 298, color: '#FFA500' },
            { label: 'Slate', value: 276, color: '#708090' },
            { label: 'Brown', value: 254, color: '#A52A2A' },
            { label: 'Silver', value: 232, color: '#C0C0C0' },
            
            // Specialty colors
            { label: 'Burgundy', value: 210, color: '#800020' },
            { label: 'Dark Green', value: 189, color: '#006400' },
            { label: 'Golden Yellow', value: 167, color: '#FFD700' },
            { label: 'Lemon', value: 145, color: '#FFFACD' },
            { label: 'Sapphire', value: 123, color: '#0F52BA' },
            { label: 'Forest Green', value: 101, color: '#228B22' },
            
            // Heather variations
            { label: 'Purple Heather', value: 89, color: '#9370DB' },
            { label: 'Red Heather', value: 78, color: '#CD5C5C' },
            { label: 'Olive Heather', value: 67, color: '#9ACD32' },
            { label: 'Pink Heather', value: 56, color: '#DDA0DD' },
            
            // Specialty/Limited colors
            { label: 'Neon Pink', value: 45, color: '#FF1493' },
            { label: 'Sage Green', value: 34, color: '#87A96B' },
            { label: 'Bright Pink', value: 28, color: '#FF69B4' },
            { label: 'Dusty Blue', value: 23, color: '#6B8CAE' },
            { label: 'Brushed Steel', value: 18, color: '#A8A8A8' },
            
            // Two-tone colors (raglan styles)
            { label: 'Black White', value: 15, color: '#000000' },
            { label: 'Navy White', value: 12, color: '#000080' },
            { label: 'Red White', value: 10, color: '#DC143C' },
            { label: 'Royal Blue White', value: 8, color: '#4169E1' },
            { label: 'Dark Heather White', value: 6, color: '#414141' },
            
            // Athletic heather variations
            { label: 'Black Athletic Heather', value: 5, color: '#2F2F2F' },
            { label: 'Navy Athletic Heather', value: 3, color: '#1C1C3A' }
        ];

        // 3. Top Products data - Using product list from the app
        const topProductsData = [
            { label: 'S. T-Shirt', value: 3245, percentage: 28.5 },
            { label: 'P. T-Shirt', value: 2876, percentage: 25.3 },
            { label: 'Hoodie', value: 1654, percentage: 14.5 },
            { label: 'Sweatshirt', value: 1234, percentage: 10.8 },
            { label: 'V-Neck', value: 987, percentage: 8.7 },
            { label: 'Tank Top', value: 765, percentage: 6.7 },
            { label: 'Mug', value: 432, percentage: 3.8 },
            { label: 'Tote Bag', value: 298, percentage: 2.6 },
            { label: 'Throw Pillow', value: 234, percentage: 2.1 },
            { label: 'Phone Case', value: 189, percentage: 1.7 },
            { label: 'Pop Socket', value: 156, percentage: 1.4 },
            { label: 'Poster', value: 123, percentage: 1.1 },
            { label: 'Sticker', value: 98, percentage: 0.9 },
            { label: 'Apron', value: 87, percentage: 0.8 },
            { label: 'Dress', value: 67, percentage: 0.6 }
        ];

        // 4. Return Rate data - Products with return percentages
        const returnRateData = [
            { label: 'Dress', value: 15.2, percentage: 15.2 },
            { label: 'Apron', value: 12.8, percentage: 12.8 },
            { label: 'Hoodie', value: 8.9, percentage: 8.9 },
            { label: 'Sweatshirt', value: 7.6, percentage: 7.6 },
            { label: 'Phone Case', value: 6.3, percentage: 6.3 },
            { label: 'Tote Bag', value: 5.1, percentage: 5.1 },
            { label: 'V-Neck', value: 4.8, percentage: 4.8 },
            { label: 'P. T-Shirt', value: 4.2, percentage: 4.2 },
            { label: 'S. T-Shirt', value: 3.9, percentage: 3.9 },
            { label: 'Tank Top', value: 3.5, percentage: 3.5 },
            { label: 'Throw Pillow', value: 2.8, percentage: 2.8 },
            { label: 'Mug', value: 2.1, percentage: 2.1 },
            { label: 'Poster', value: 1.6, percentage: 1.6 },
            { label: 'Pop Socket', value: 1.2, percentage: 1.2 },
            { label: 'Sticker', value: 0.8, percentage: 0.8 }
        ];

        // 5. Sold Sizes data - Size distribution with sales values
        const soldSizesData = [
            { label: 'L', value: 3245, percentage: 24.8 },
            { label: 'M', value: 2876, percentage: 22.0 },
            { label: 'XL', value: 2134, percentage: 16.3 },
            { label: 'S', value: 1876, percentage: 14.3 },
            { label: '2XL', value: 1234, percentage: 9.4 },
            { label: '3XL', value: 876, percentage: 6.7 },
            { label: 'XS', value: 654, percentage: 5.0 },
            { label: '4XL', value: 198, percentage: 1.5 }
        ];

        // 6. Sales by Marketplace data - 7 main marketplaces with system colors
        const salesByMarketplaceData = [
            { label: 'US', value: 4567, percentage: 45.2 },
            { label: 'UK', value: 1234, percentage: 12.2 },
            { label: 'DE', value: 1156, percentage: 11.4 },
            { label: 'FR', value: 876, percentage: 8.7 },
            { label: 'IT', value: 654, percentage: 6.5 },
            { label: 'ES', value: 543, percentage: 5.4 },
            { label: 'JP', value: 321, percentage: 3.2 }
        ];
        */

        // Initialize all 6 pie charts
        const fitTypeChart = new SnapChart({
            container: '#fitTypeChart',
            type: 'pie',
            data: fitTypeData,
            options: {
                animate: true,
                responsive: true,
                centerLabel: 'Fit Type'
            }
        });

        const soldColorsChart = new SnapChart({
            container: '#soldColorsChart',
            type: 'pie',
            data: soldColorsData,
            options: {
                animate: true,
                responsive: true,
                centerLabel: 'Sold Colors',
                showSliceBorders: true // Enable borders for sold colors visibility
            }
        });

        const topProductsChart = new SnapChart({
            container: '#topProductsChart',
            type: 'pie',
            data: topProductsData,
            options: {
                animate: true,
                responsive: true,
                centerLabel: 'Top Products'
            }
        });

        const returnRateChart = new SnapChart({
            container: '#returnRateChart',
            type: 'pie',
            data: returnRateData,
            options: {
                animate: true,
                responsive: true,
                centerLabel: 'Returns Rate'
            }
        });

        const soldSizesChart = new SnapChart({
            container: '#soldSizesChart',
            type: 'pie',
            data: soldSizesData,
            options: {
                animate: true,
                responsive: true,
                centerLabel: 'Sold Sizes'
            }
        });

        const salesByMarketplaceChart = new SnapChart({
            container: '#salesByMarketplaceChart',
            type: 'pie',
            data: salesByMarketplaceData,
            options: {
                animate: true,
                responsive: true,
                centerLabel: 'Sales by\nMarketplace' // Multi-line center text
            }
        });

        // Theme controls
        document.getElementById('lightTheme').addEventListener('click', function() {
            document.documentElement.setAttribute('data-theme', 'light');
            document.querySelectorAll('.control-btn').forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
        });
        
        document.getElementById('darkTheme').addEventListener('click', function() {
            document.documentElement.setAttribute('data-theme', 'dark');
            document.querySelectorAll('.control-btn').forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
        });

        // Log chart initialization
        console.log('6 Pie Charts initialized');
        console.log('Fit Type data:', fitTypeData);
        console.log('Sold Colors data:', soldColorsData);
        console.log('Top Products data:', topProductsData);
        console.log('Return Rate data:', returnRateData);
        console.log('Sold Sizes data:', soldSizesData);
        }
        
        // Initialize pie charts when page loads
        initializePieCharts().catch(error => {
            console.error('Failed to initialize pie charts:', error);
        });
    </script>
</body>
</html> 