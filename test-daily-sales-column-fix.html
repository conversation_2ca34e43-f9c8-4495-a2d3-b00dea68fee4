<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daily Sales Chart Column Width Fix Test</title>
    <link rel="stylesheet" href="components/charts/snap-charts.css">
    <style>
        body {
            font-family: 'Amazon Ember', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #232f3e;
        }
        .test-description {
            color: #666;
            margin-bottom: 15px;
            font-size: 14px;
        }
        .chart-container {
            width: 100%;
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .info-panel {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-top: 15px;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <h1>Daily Sales Chart: Column Width, Performance, Slider & Tooltip Optimization Test</h1>
    <p>Testing fixes for: 1) Overlapping columns, 2) Performance with large datasets, 3) Minimum 50px slider width, 4) Tooltips only for ≤31 days.</p>

    <!-- Test 1: 7 days (should have wider columns) -->
    <div class="test-container">
        <div class="test-title">Test 1: Last 7 Days (Few Columns)</div>
        <div class="test-description">
            With only 7 data points, columns should be wider and well-spaced.
        </div>
        <div id="chart1" class="chart-container"></div>
        <div class="info-panel">
            Expected: Wide columns with good spacing, no overlapping
        </div>
    </div>

    <!-- Test 2: 30 days (should have medium columns) -->
    <div class="test-container">
        <div class="test-title">Test 2: Last 30 Days (Medium Columns)</div>
        <div class="test-description">
            With 30 data points, columns should be medium width and properly distributed.
        </div>
        <div id="chart2" class="chart-container"></div>
        <div class="info-panel">
            Expected: Medium-width columns evenly distributed across chart width
        </div>
    </div>

    <!-- Test 3: 90 days (should have narrow columns) -->
    <div class="test-container">
        <div class="test-title">Test 3: Last 90 Days (Many Columns)</div>
        <div class="test-description">
            With 90 data points, columns should be narrow but still visible and not overlapping.
        </div>
        <div id="chart3" class="chart-container"></div>
        <div class="info-panel">
            Expected: Narrow columns but no overlapping, proper spacing maintained
        </div>
    </div>

    <!-- Test 4: 3 days (should have reasonable width, not overly wide) -->
    <div class="test-container">
        <div class="test-title">Test 4: Current Month (3 Days - Few Columns)</div>
        <div class="test-description">
            With only 3 data points, columns should be reasonably sized, not overly wide.
        </div>
        <div id="chart4" class="chart-container"></div>
        <div class="info-panel">
            Expected: Reasonable column width (max 32px), not taking up entire chart width
        </div>
    </div>

    <!-- Test 5: 90 days (should have throttled hover) -->
    <div class="test-container">
        <div class="test-title">Test 5: Last 90 Days (Throttled Hover)</div>
        <div class="test-description">
            With 90 data points, this tests the throttled hover performance optimization.
        </div>
        <div id="chart5" class="chart-container"></div>
        <div class="info-panel">
            Expected: Narrow columns, <strong>no tooltips</strong> for better performance (>31 days).
        </div>
    </div>

    <!-- Test 6: 90 days (should have no hover for performance) -->
    <div class="test-container">
        <div class="test-title">Test 6: Month Boundary Test (90 Days - No Tooltips)</div>
        <div class="test-description">
            With 90 data points, tooltips are disabled for optimal performance (>31 days).
        </div>
        <div id="chart6" class="chart-container"></div>
        <div class="info-panel">
            Expected: <strong>No tooltips</strong> (>31 days). Use slider to zoom into ≤31 days to see tooltips.
        </div>
    </div>

    <!-- Performance Test Instructions -->
    <div class="test-container">
        <div class="test-title">🚀 Performance Test Instructions</div>
        <div class="test-description">
            <strong>Test the slider performance improvements:</strong><br>
            1. Use the slider on the 150-day chart above<br>
            2. Drag the slider handles and range body rapidly<br>
            3. Try to resize the slider to less than 50px width<br>
            4. The chart should remain responsive and the slider should never go below 50px
        </div>
        <div class="info-panel">
            <strong>Expected Results:</strong><br>
            ✅ Smooth slider dragging without lag/freeze<br>
            ✅ Slider width never goes below 50px<br>
            ✅ Chart updates smoothly during slider interaction<br>
            ✅ No performance issues with large datasets
        </div>
    </div>

    <!-- Tooltip Optimization Test Instructions -->
    <div class="test-container">
        <div class="test-title">🎯 Tooltip Optimization Test</div>
        <div class="test-description">
            <strong>Test the tooltip optimization:</strong><br>
            1. <strong>Charts 1-4 (≤31 days):</strong> Hover over columns - tooltips should appear<br>
            2. <strong>Charts 5-6 (>31 days):</strong> Hover over columns - no tooltips should appear<br>
            3. <strong>Use the slider</strong> on charts 5-6 to zoom into ≤31 days<br>
            4. <strong>After zooming:</strong> Tooltips should now appear when hovering<br>
            5. This ensures tooltips only show when there's enough space for accuracy
        </div>
        <div class="info-panel">
            <strong>Expected Results:</strong><br>
            ✅ Tooltips appear only when ≤31 days are visible<br>
            ✅ No tooltips when >31 days are visible (prevents positioning issues)<br>
            ✅ Slider zoom enables tooltips by reducing visible days to ≤31<br>
            ✅ Better performance and user experience with large datasets
        </div>
    </div>

    <script src="components/charts/snap-charts.js"></script>
    <script>
        // Generate test data for different time periods
        function generateDailySalesData(days) {
            const data = [];
            const today = new Date();

            for (let i = days - 1; i >= 0; i--) {
                const date = new Date(today);
                date.setDate(today.getDate() - i);

                const sales = Math.floor(Math.random() * 50) + 5; // 5-55 sales
                const royalties = Math.floor(sales * (Math.random() * 3 + 1)); // 1-4x sales
                const returns = Math.floor(Math.random() * 5); // 0-5 returns

                data.push({
                    month: date.toLocaleDateString('en-US', { month: 'short' }).toUpperCase(),
                    day: date.getDate().toString().padStart(2, '0'),
                    year: date.getFullYear().toString().slice(-2),
                    sales: sales,
                    royalties: royalties,
                    returns: returns,
                    dateObj: new Date(date)
                });
            }

            return data;
        }

        // Generate test data starting from the 1st of a month for accurate label testing
        function generateMonthBoundaryTestData() {
            const data = [];
            // Start from August 1st, 2025 and generate 90 days
            const startDate = new Date(2025, 7, 1); // August 1st, 2025 (month is 0-indexed)

            for (let i = 0; i < 90; i++) {
                const date = new Date(startDate);
                date.setDate(startDate.getDate() + i);

                const sales = Math.floor(Math.random() * 50) + 5; // 5-55 sales
                const royalties = Math.floor(sales * (Math.random() * 3 + 1)); // 1-4x sales
                const returns = Math.floor(Math.random() * 5); // 0-5 returns

                data.push({
                    month: date.toLocaleDateString('en-US', { month: 'short' }).toUpperCase(),
                    day: date.getDate().toString().padStart(2, '0'),
                    year: date.getFullYear().toString().slice(-2),
                    sales: sales,
                    royalties: royalties,
                    returns: returns,
                    dateObj: new Date(date)
                });
            }

            return data;
        }

        // Test 1: 7 days
        const chart1 = new SnapChart({
            container: '#chart1',
            type: 'daily-sales-history',
            data: generateDailySalesData(7),
            options: {
                title: 'Daily Sales History - 7 Days',
                allTimeData: generateDailySalesData(7)
            },
            demoOptions: {
                showContainer: true,
                showTitle: false,
                showDataEditor: false,
                showControls: false,
                showInsights: false
            }
        });

        // Test 2: 30 days
        const chart2 = new SnapChart({
            container: '#chart2',
            type: 'daily-sales-history',
            data: generateDailySalesData(30),
            options: {
                title: 'Daily Sales History - 30 Days',
                allTimeData: generateDailySalesData(30)
            },
            demoOptions: {
                showContainer: true,
                showTitle: false,
                showDataEditor: false,
                showControls: false,
                showInsights: false
            }
        });

        // Test 3: 90 days
        const chart3 = new SnapChart({
            container: '#chart3',
            type: 'daily-sales-history',
            data: generateDailySalesData(90),
            options: {
                title: 'Daily Sales History - 90 Days',
                allTimeData: generateDailySalesData(90)
            },
            demoOptions: {
                showContainer: true,
                showTitle: false,
                showDataEditor: false,
                showControls: false,
                showInsights: false
            }
        });

        // Test 4: 3 days (few columns test)
        const chart4 = new SnapChart({
            container: '#chart4',
            type: 'daily-sales-history',
            data: generateDailySalesData(3),
            options: {
                title: 'Daily Sales History - 3 Days',
                allTimeData: generateDailySalesData(3)
            },
            demoOptions: {
                showContainer: true,
                showTitle: false,
                showDataEditor: false,
                showControls: false,
                showInsights: false
            }
        });

        // Test 5: 90 days (throttled hover)
        const chart5 = new SnapChart({
            container: '#chart5',
            type: 'daily-sales-history',
            data: generateDailySalesData(90),
            options: {
                title: 'Daily Sales History - 90 Days',
                allTimeData: generateDailySalesData(90)
            },
            demoOptions: {
                showContainer: true,
                showTitle: false,
                showDataEditor: false,
                showControls: false,
                showInsights: false
            }
        });

        // Test 6: Month boundary test (starts from Aug 1st for accurate label testing)
        const chart6 = new SnapChart({
            container: '#chart6',
            type: 'daily-sales-history',
            data: generateMonthBoundaryTestData(),
            options: {
                title: 'Daily Sales History - Month Boundary Test (Aug 1 - Oct 30)',
                allTimeData: generateMonthBoundaryTestData()
            },
            demoOptions: {
                showContainer: true,
                showTitle: false,
                showDataEditor: false,
                showControls: false,
                showInsights: false
            }
        });

        // Log column width calculations for debugging
        console.log('Column width and performance test completed. Check charts for proper spacing and hover behavior.');
    </script>
</body>
</html>
