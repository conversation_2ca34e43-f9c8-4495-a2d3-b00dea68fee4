<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Timezone Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      padding: 20px;
      background: #f5f5f5;
    }
    .test-container {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      margin-bottom: 20px;
    }
    .test-result {
      margin: 10px 0;
      padding: 10px;
      border-radius: 4px;
    }
    .success {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    .info {
      background: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }
    .error {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
  </style>
</head>
<body>
  <h1>Pacific Time Synchronization Test</h1>
  
  <div class="test-container">
    <h2>Timezone Utility Test</h2>
    <div id="timezone-test-results"></div>
  </div>

  <div class="test-container">
    <h2>Dashboard Functions Test</h2>
    <div id="dashboard-test-results"></div>
  </div>

  <!-- Load timezone utility -->
  <script src="utils/timezone.js"></script>
  
  <script>
    function addResult(containerId, message, type = 'info') {
      const container = document.getElementById(containerId);
      const div = document.createElement('div');
      div.className = `test-result ${type}`;
      div.textContent = message;
      container.appendChild(div);
    }

    function testTimezoneUtility() {
      try {
        // Test if SnapTimezone is available
        if (typeof window.SnapTimezone === 'undefined') {
          addResult('timezone-test-results', 'ERROR: SnapTimezone utility not loaded', 'error');
          return;
        }

        addResult('timezone-test-results', 'SUCCESS: SnapTimezone utility loaded', 'success');

        // Test getPacificTime
        const pacificTime = window.SnapTimezone.getPacificTime();
        addResult('timezone-test-results', `Pacific Time: ${pacificTime.toString()}`, 'info');

        // Test getPacificDate
        const pacificDate = window.SnapTimezone.getPacificDate();
        addResult('timezone-test-results', `Pacific Date (midnight): ${pacificDate.toString()}`, 'info');

        // Test formatPacificDate
        const formattedDate = window.SnapTimezone.formatPacificDate(pacificTime, {
          month: 'short',
          day: 'numeric',
          year: 'numeric'
        });
        addResult('timezone-test-results', `Formatted Date: ${formattedDate}`, 'info');

        // Test getPacificMonthDay
        const monthDay = window.SnapTimezone.getPacificMonthDay();
        addResult('timezone-test-results', `Month/Day: ${monthDay.month + 1}/${monthDay.day} (month is 0-based)`, 'info');

        // Compare with current UTC time
        const utcTime = new Date();
        addResult('timezone-test-results', `UTC Time: ${utcTime.toString()}`, 'info');
        
        const timeDiff = Math.abs(pacificTime.getTime() - utcTime.getTime()) / (1000 * 60 * 60);
        addResult('timezone-test-results', `Time difference: ${timeDiff.toFixed(1)} hours`, 'info');

      } catch (error) {
        addResult('timezone-test-results', `ERROR: ${error.message}`, 'error');
      }
    }

    function testDashboardFunctions() {
      try {
        // Simulate the dashboard date formatting functions
        const today = window.SnapTimezone.getPacificTime();
        
        // Test Today's Sales Card format
        const todayFormatted = window.SnapTimezone.formatPacificDate(today, {
          month: 'short',
          day: 'numeric',
          year: 'numeric'
        });
        addResult('dashboard-test-results', `Today's Sales Card format: ${todayFormatted}`, 'info');

        // Test Today vs Previous Years format
        const chartTitleFormat = window.SnapTimezone.formatPacificDate(today, {
          month: 'short',
          day: 'numeric'
        });
        addResult('dashboard-test-results', `Chart Title format: ${chartTitleFormat}`, 'info');

        // Test chart data generation
        const monthDay = window.SnapTimezone.getPacificMonthDay();
        const testYear = 2025;
        const yearDate = new Date(testYear, monthDay.month, monthDay.day);
        const monthAbbreviation = yearDate.toLocaleDateString('en-US', { month: 'short' });
        const day = yearDate.getDate();
        const yearAbbreviation = testYear.toString().slice(-2);
        
        addResult('dashboard-test-results', `Chart Data - Month Day: ${monthAbbreviation} ${day}`, 'info');
        addResult('dashboard-test-results', `Chart Data - Year: '${yearAbbreviation}`, 'info');

        addResult('dashboard-test-results', 'SUCCESS: All dashboard functions working with centralized timezone', 'success');

      } catch (error) {
        addResult('dashboard-test-results', `ERROR: ${error.message}`, 'error');
      }
    }

    // Run tests when page loads
    window.addEventListener('load', function() {
      setTimeout(() => {
        testTimezoneUtility();
        testDashboardFunctions();
      }, 100);
    });
  </script>
</body>
</html>
