<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comparison Percentage Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .test-result.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-result.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-result.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .comparison-percentage {
            font-family: 'Amazon Ember', sans-serif;
            font-size: 12px;
            font-weight: 500;
            line-height: 16px;
        }
        .comparison-percentage.positive {
            color: #04AE2C;
        }
        .comparison-percentage.negative {
            color: #FF391F;
        }
        .mock-sales-card {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .comparison-container {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 10px;
        }
        .comparison-content {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        .comparison-label {
            font-size: 12px;
            color: #687078;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Comparison Percentage Functionality Test</h1>
        
        <div class="test-section">
            <h2>Test 1: Percentage Calculation Logic</h2>
            <div id="calculation-tests"></div>
        </div>

        <div class="test-section">
            <h2>Test 2: UI Update Functionality</h2>
            <div id="ui-tests"></div>
        </div>

        <div class="test-section">
            <h2>Test 3: Edge Cases</h2>
            <div id="edge-case-tests"></div>
        </div>
    </div>

    <script>
        // Import the functions from dashboard.js
        // Note: In a real scenario, these would be imported from the actual file
        
        /**
         * Calculate percentage change between two values
         */
        function calculateComparisonPercentage(currentValue, previousValue) {
            // Handle edge cases
            if (previousValue === 0) {
                return {
                    percentage: null,
                    isPositive: false,
                    isValid: false,
                    message: 'No previous data available'
                };
            }

            if (currentValue === null || previousValue === null) {
                return {
                    percentage: null,
                    isPositive: false,
                    isValid: false,
                    message: 'Missing data'
                };
            }

            // Calculate percentage change
            const percentageChange = ((currentValue - previousValue) / previousValue) * 100;
            const roundedPercentage = Math.round(percentageChange * 10) / 10; // Round to 1 decimal place

            return {
                percentage: roundedPercentage,
                isPositive: roundedPercentage >= 0,
                isValid: true,
                message: null
            };
        }

        /**
         * Get comparison label for a specific period
         */
        function getComparisonLabel(period) {
            const labels = {
                currentMonth: 'Compared to last month',
                lastMonth: 'Compared to month before last',
                currentYear: 'Compared to last year',
                lastYear: 'Compared to year before last'
            };
            
            return labels[period] || 'Compared to previous period';
        }

        /**
         * Update comparison container with calculated percentage
         */
        function updateComparisonContainer(salesCard, comparisonData, comparisonLabel) {
            const comparisonContainer = salesCard.querySelector('.comparison-container');
            if (!comparisonContainer) {
                return false;
            }

            const comparisonPercentage = comparisonContainer.querySelector('.comparison-percentage');
            const comparisonLabelElement = comparisonContainer.querySelector('.comparison-label');

            if (!comparisonPercentage || !comparisonLabelElement) {
                return false;
            }

            if (!comparisonData.isValid) {
                comparisonContainer.style.display = 'none';
                return true;
            }

            // Show comparison container
            comparisonContainer.style.display = 'flex';

            // Update percentage text and styling
            const percentageText = comparisonData.isPositive ? 
                `+${comparisonData.percentage}%` : 
                `${comparisonData.percentage}%`;

            comparisonPercentage.textContent = percentageText;
            
            // Update color classes
            comparisonPercentage.classList.remove('positive', 'negative');
            comparisonPercentage.classList.add(comparisonData.isPositive ? 'positive' : 'negative');

            // Update comparison label
            comparisonLabelElement.textContent = comparisonLabel;

            return true;
        }

        // Test functions
        function runCalculationTests() {
            const tests = [
                { current: 100, previous: 80, expected: 25.0, description: 'Positive growth' },
                { current: 80, previous: 100, expected: -20.0, description: 'Negative growth' },
                { current: 100, previous: 0, expected: null, description: 'Division by zero' },
                { current: 100, previous: 100, expected: 0.0, description: 'No change' },
                { current: 0, previous: 100, expected: -100.0, description: 'Complete loss' }
            ];

            const resultsDiv = document.getElementById('calculation-tests');
            resultsDiv.innerHTML = '';

            tests.forEach((test, index) => {
                const result = calculateComparisonPercentage(test.current, test.previous);
                const isSuccess = result.isValid ? 
                    (result.percentage === test.expected) : 
                    (test.expected === null);

                const resultDiv = document.createElement('div');
                resultDiv.className = `test-result ${isSuccess ? 'success' : 'error'}`;
                resultDiv.innerHTML = `
                    <strong>Test ${index + 1}:</strong> ${test.description}<br>
                    Current: ${test.current}, Previous: ${test.previous}<br>
                    Expected: ${test.expected}, Got: ${result.percentage}<br>
                    Valid: ${result.isValid}, Message: ${result.message || 'N/A'}
                `;
                resultsDiv.appendChild(resultDiv);
            });
        }

        function runUITests() {
            const resultsDiv = document.getElementById('ui-tests');
            resultsDiv.innerHTML = '';

            // Create mock sales card
            const mockCard = document.createElement('div');
            mockCard.className = 'mock-sales-card';
            mockCard.innerHTML = `
                <div class="comparison-container">
                    <div class="comparison-content">
                        <span class="comparison-percentage">-1.2%</span>
                    </div>
                    <span class="comparison-label">Compared to last month</span>
                </div>
            `;

            resultsDiv.appendChild(mockCard);

            // Test positive percentage
            const positiveTest = calculateComparisonPercentage(120, 100);
            const positiveSuccess = updateComparisonContainer(mockCard, positiveTest, 'Test positive growth');
            
            const positiveResult = document.createElement('div');
            positiveResult.className = `test-result ${positiveSuccess ? 'success' : 'error'}`;
            positiveResult.innerHTML = `
                <strong>Positive Percentage Test:</strong><br>
                Result: ${positiveTest.percentage}% (${positiveTest.isPositive ? 'Positive' : 'Negative'})<br>
                UI Update: ${positiveSuccess ? 'Success' : 'Failed'}
            `;
            resultsDiv.appendChild(positiveResult);

            // Test negative percentage
            const negativeTest = calculateComparisonPercentage(80, 100);
            const negativeSuccess = updateComparisonContainer(mockCard, negativeTest, 'Test negative growth');
            
            const negativeResult = document.createElement('div');
            negativeResult.className = `test-result ${negativeSuccess ? 'success' : 'error'}`;
            negativeResult.innerHTML = `
                <strong>Negative Percentage Test:</strong><br>
                Result: ${negativeTest.percentage}% (${negativeTest.isPositive ? 'Positive' : 'Negative'})<br>
                UI Update: ${negativeSuccess ? 'Success' : 'Failed'}
            `;
            resultsDiv.appendChild(negativeResult);
        }

        function runEdgeCaseTests() {
            const resultsDiv = document.getElementById('edge-case-tests');
            resultsDiv.innerHTML = '';

            const edgeCases = [
                { current: 100, previous: 0, description: 'Previous value is zero' },
                { current: null, previous: 100, description: 'Current value is null' },
                { current: 100, previous: null, description: 'Previous value is null' },
                { current: 0, previous: 0, description: 'Both values are zero' }
            ];

            edgeCases.forEach((test, index) => {
                const result = calculateComparisonPercentage(test.current, test.previous);
                
                const resultDiv = document.createElement('div');
                resultDiv.className = `test-result ${!result.isValid ? 'success' : 'error'}`;
                resultDiv.innerHTML = `
                    <strong>Edge Case ${index + 1}:</strong> ${test.description}<br>
                    Current: ${test.current}, Previous: ${test.previous}<br>
                    Valid: ${result.isValid}, Message: ${result.message || 'N/A'}
                `;
                resultsDiv.appendChild(resultDiv);
            });
        }

        // Run all tests when page loads
        window.addEventListener('load', function() {
            runCalculationTests();
            runUITests();
            runEdgeCaseTests();
        });
    </script>
</body>
</html> 